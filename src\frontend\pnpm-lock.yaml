lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

overrides:
  prismjs: 1.30.0

importers:

  .:
    dependencies:
      '@fluentui-copilot/react-copilot':
        specifier: 0.23.3
        version: 0.23.3(2cabaf760f2a31c97b9cc994068d61b2)
      '@fluentui-copilot/react-copilot-chat':
        specifier: 0.9.6
        version: 0.9.6(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-tabster@9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-feedback-buttons':
        specifier: 0.9.6
        version: 0.9.6(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-provider':
        specifier: 0.9.4
        version: 0.9.4(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-reference':
        specifier: 0.13.10
        version: 0.13.10(@fluentui/keyboard-keys@9.0.8)(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-sensitivity-label':
        specifier: 0.5.8
        version: 0.5.8(@fluentui/keyboard-keys@9.0.8)(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-components':
        specifier: 9.60.0
        version: 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons':
        specifier: 2.0.279
        version: 2.0.279(react@19.1.0)
      '@fluentui/react-theme':
        specifier: 9.1.24
        version: 9.1.24
      '@vitejs/plugin-react':
        specifier: 4.4.1
        version: 4.4.1(vite@6.3.4(@types/node@22.14.1))
      clsx:
        specifier: 2.1.1
        version: 2.1.1
      copy-to-clipboard:
        specifier: ^3.3.3
        version: 3.3.3
      prismjs:
        specifier: 1.30.0
        version: 1.30.0
      react:
        specifier: 19.1.0
        version: 19.1.0
      react-dom:
        specifier: 19.1.0
        version: 19.1.0(react@19.1.0)
      react-markdown:
        specifier: 10.1.0
        version: 10.1.0(@types/react@18.3.20)(react@19.1.0)
      react-syntax-highlighter:
        specifier: ^15.5.0
        version: 15.6.1(react@19.1.0)
      rehype-katex:
        specifier: ^7.0.0
        version: 7.0.1
      rehype-raw:
        specifier: ^7.0.0
        version: 7.0.0
      rehype-sanitize:
        specifier: ^6.0.0
        version: 6.0.0
      rehype-stringify:
        specifier: ^10.0.0
        version: 10.0.1
      remark-breaks:
        specifier: ^4.0.0
        version: 4.0.0
      remark-gfm:
        specifier: ^4.0.0
        version: 4.0.1
      remark-math:
        specifier: ^6.0.0
        version: 6.0.0
      remark-parse:
        specifier: ^11.0.0
        version: 11.0.0
      remark-supersub:
        specifier: ^1.0.0
        version: 1.0.0
      vite:
        specifier: 6.3.4
        version: 6.3.4(@types/node@22.14.1)
    devDependencies:
      '@types/node':
        specifier: 22.14.1
        version: 22.14.1
      '@types/react':
        specifier: 18.3.20
        version: 18.3.20
      '@types/react-dom':
        specifier: 18.3.5
        version: 18.3.5(@types/react@18.3.20)
      '@types/react-syntax-highlighter':
        specifier: 15.5.13
        version: 15.5.13
      typescript:
        specifier: 5.8.3
        version: 5.8.3

packages:

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==}
    engines: {node: '>=6.0.0'}

  '@babel/code-frame@7.26.2':
    resolution: {integrity: sha512-RJlIHRueQgwWitWgF8OdFYGZX328Ax5BCemNGlqHfplnRT9ESi8JkFlvaVYbS+UubVY6dpv87Fs2u5M29iNFVQ==}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.26.8':
    resolution: {integrity: sha512-oH5UPLMWR3L2wEFLnFJ1TZXqHufiTKAiLfqw5zkhS4dKXLJ10yVztfil/twG8EDTA4F/tvVNw9nOl4ZMslB8rQ==}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.26.10':
    resolution: {integrity: sha512-vMqyb7XCDMPvJFFOaT9kxtiRh42GwlZEg1/uIgtZshS5a/8OaduUfCi7kynKgc3Tw/6Uo2D+db9qBttghhmxwQ==}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.27.0':
    resolution: {integrity: sha512-VybsKvpiN1gU1sdMZIp7FcqphVVKEwcuj02x73uvcHE0PTihx1nlBcowYWhDwjpoAXRv43+gDzyggGnn1XZhVw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.27.0':
    resolution: {integrity: sha512-LVk7fbXml0H2xH34dFzKQ7TDZ2G4/rVTOrq9V+icbbadjbVxxeFeDsNHv2SrZeWoA+6ZiTyWYWtScEIW07EAcA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.25.9':
    resolution: {integrity: sha512-tnUA4RsrmflIM6W6RFTLFSXITtl0wKjgpnLgXyowocVPrbYrLUXSBXDgTs8BlbmIzIdlBySRQjINYs2BAkiLtw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.26.0':
    resolution: {integrity: sha512-xO+xu6B5K2czEnQye6BHA7DolFFmS3LB7stHZFaOLb1pAwO1HWLS8fXA+eh0A2yIvltPVmx3eNNDBJA2SLHXFw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-plugin-utils@7.26.5':
    resolution: {integrity: sha512-RS+jZcRdZdRFzMyr+wcsaqOmld1/EqTghfaBGQQd/WnRdzdlvSZ//kF7U8VQTxf1ynZ4cjUcYgjVGx13ewNPMg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.25.9':
    resolution: {integrity: sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.25.9':
    resolution: {integrity: sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.25.9':
    resolution: {integrity: sha512-e/zv1co8pp55dNdEcCynfj9X7nyUKUXoUEwfXqaZt0omVOmDe9oOTdKStH4GmAw6zxMFs50ZayuMfHDKlO7Tfw==}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.27.0':
    resolution: {integrity: sha512-U5eyP/CTFPuNE3qk+WZMxFkp/4zUzdceQlfzf7DdGdhp+Fezd7HD+i8Y24ZuTMKX3wQBld449jijbGq6OdGNQg==}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.27.0':
    resolution: {integrity: sha512-iaepho73/2Pz7w2eMS0Q5f83+0RKI7i4xmiYeBmDzfRVbQtTOG7Ts0S4HzJVsTMGI9keU8rNfuZr8DKfSt7Yyg==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-transform-react-jsx-self@7.25.9':
    resolution: {integrity: sha512-y8quW6p0WHkEhmErnfe58r7x0A70uKphQm8Sp8cV7tjNQwK56sNVK0M73LK3WuYmsuyrftut4xAkjjgU0twaMg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx-source@7.25.9':
    resolution: {integrity: sha512-+iqjT8xmXhhYv4/uiYd8FNQsraMFZIfxVSqxxVSZP0WbbSAWvBXAul0m/zu+7Vv4O/3WtApy9pmaTMiumEZgfg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/runtime@7.27.1':
    resolution: {integrity: sha512-1x3D2xEk2fRo3PAhwQwu5UubzgiVWSXTBfWpVd2Mx2AzRqJuDJCsgaDVZ7HB5iGzDW1Hl1sWN2mFyKjmR9uAog==}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.27.0':
    resolution: {integrity: sha512-2ncevenBqXI6qRMukPlXwHKHchC7RyMuu4xv5JBXRfOGVcTy1mXCD12qrp7Jsoxll1EV3+9sE4GugBVRjT2jFA==}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.27.0':
    resolution: {integrity: sha512-19lYZFzYVQkkHkl4Cy4WrAVcqBkgvV2YM2TU3xG6DIwO7O3ecbDPfW3yM3bjAGcqcQHi+CCtjMR3dIEHxsd6bA==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.27.0':
    resolution: {integrity: sha512-H45s8fVLYjbhFH62dIJ3WtmJ6RSPt/3DRO0ZcT2SUiYiQyz3BLVb9ADEnLl91m74aQPS3AzzeajZHYOalWe3bg==}
    engines: {node: '>=6.9.0'}

  '@emotion/hash@0.9.2':
    resolution: {integrity: sha512-MyqliTZGuOm3+5ZRSaaBGP3USLw6+EGykkwZns2EPC5g8jJ4z9OrdZY9apkl3+UP9+sdz76YYkwCKP5gh8iY3g==}

  '@esbuild/aix-ppc64@0.25.3':
    resolution: {integrity: sha512-W8bFfPA8DowP8l//sxjJLSLkD8iEjMc7cBVyP+u4cEv9sM7mdUCkgsj+t0n/BWPFtv7WWCN5Yzj0N6FJNUUqBQ==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [aix]

  '@esbuild/android-arm64@0.25.3':
    resolution: {integrity: sha512-XelR6MzjlZuBM4f5z2IQHK6LkK34Cvv6Rj2EntER3lwCBFdg6h2lKbtRjpTTsdEjD/WSe1q8UyPBXP1x3i/wYQ==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.25.3':
    resolution: {integrity: sha512-PuwVXbnP87Tcff5I9ngV0lmiSu40xw1At6i3GsU77U7cjDDB4s0X2cyFuBiDa1SBk9DnvWwnGvVaGBqoFWPb7A==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.25.3':
    resolution: {integrity: sha512-ogtTpYHT/g1GWS/zKM0cc/tIebFjm1F9Aw1boQ2Y0eUQ+J89d0jFY//s9ei9jVIlkYi8AfOjiixcLJSGNSOAdQ==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.25.3':
    resolution: {integrity: sha512-eESK5yfPNTqpAmDfFWNsOhmIOaQA59tAcF/EfYvo5/QWQCzXn5iUSOnqt3ra3UdzBv073ykTtmeLJZGt3HhA+w==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.25.3':
    resolution: {integrity: sha512-Kd8glo7sIZtwOLcPbW0yLpKmBNWMANZhrC1r6K++uDR2zyzb6AeOYtI6udbtabmQpFaxJ8uduXMAo1gs5ozz8A==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.25.3':
    resolution: {integrity: sha512-EJiyS70BYybOBpJth3M0KLOus0n+RRMKTYzhYhFeMwp7e/RaajXvP+BWlmEXNk6uk+KAu46j/kaQzr6au+JcIw==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.25.3':
    resolution: {integrity: sha512-Q+wSjaLpGxYf7zC0kL0nDlhsfuFkoN+EXrx2KSB33RhinWzejOd6AvgmP5JbkgXKmjhmpfgKZq24pneodYqE8Q==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.25.3':
    resolution: {integrity: sha512-xCUgnNYhRD5bb1C1nqrDV1PfkwgbswTTBRbAd8aH5PhYzikdf/ddtsYyMXFfGSsb/6t6QaPSzxtbfAZr9uox4A==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.25.3':
    resolution: {integrity: sha512-dUOVmAUzuHy2ZOKIHIKHCm58HKzFqd+puLaS424h6I85GlSDRZIA5ycBixb3mFgM0Jdh+ZOSB6KptX30DD8YOQ==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.25.3':
    resolution: {integrity: sha512-yplPOpczHOO4jTYKmuYuANI3WhvIPSVANGcNUeMlxH4twz/TeXuzEP41tGKNGWJjuMhotpGabeFYGAOU2ummBw==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.25.3':
    resolution: {integrity: sha512-P4BLP5/fjyihmXCELRGrLd793q/lBtKMQl8ARGpDxgzgIKJDRJ/u4r1A/HgpBpKpKZelGct2PGI4T+axcedf6g==}
    engines: {node: '>=18'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.25.3':
    resolution: {integrity: sha512-eRAOV2ODpu6P5divMEMa26RRqb2yUoYsuQQOuFUexUoQndm4MdpXXDBbUoKIc0iPa4aCO7gIhtnYomkn2x+bag==}
    engines: {node: '>=18'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.25.3':
    resolution: {integrity: sha512-ZC4jV2p7VbzTlnl8nZKLcBkfzIf4Yad1SJM4ZMKYnJqZFD4rTI+pBG65u8ev4jk3/MPwY9DvGn50wi3uhdaghg==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.25.3':
    resolution: {integrity: sha512-LDDODcFzNtECTrUUbVCs6j9/bDVqy7DDRsuIXJg6so+mFksgwG7ZVnTruYi5V+z3eE5y+BJZw7VvUadkbfg7QA==}
    engines: {node: '>=18'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.25.3':
    resolution: {integrity: sha512-s+w/NOY2k0yC2p9SLen+ymflgcpRkvwwa02fqmAwhBRI3SC12uiS10edHHXlVWwfAagYSY5UpmT/zISXPMW3tQ==}
    engines: {node: '>=18'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.25.3':
    resolution: {integrity: sha512-nQHDz4pXjSDC6UfOE1Fw9Q8d6GCAd9KdvMZpfVGWSJztYCarRgSDfOVBY5xwhQXseiyxapkiSJi/5/ja8mRFFA==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-arm64@0.25.3':
    resolution: {integrity: sha512-1QaLtOWq0mzK6tzzp0jRN3eccmN3hezey7mhLnzC6oNlJoUJz4nym5ZD7mDnS/LZQgkrhEbEiTn515lPeLpgWA==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [netbsd]

  '@esbuild/netbsd-x64@0.25.3':
    resolution: {integrity: sha512-i5Hm68HXHdgv8wkrt+10Bc50zM0/eonPb/a/OFVfB6Qvpiirco5gBA5bz7S2SHuU+Y4LWn/zehzNX14Sp4r27g==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-arm64@0.25.3':
    resolution: {integrity: sha512-zGAVApJEYTbOC6H/3QBr2mq3upG/LBEXr85/pTtKiv2IXcgKV0RT0QA/hSXZqSvLEpXeIxah7LczB4lkiYhTAQ==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openbsd]

  '@esbuild/openbsd-x64@0.25.3':
    resolution: {integrity: sha512-fpqctI45NnCIDKBH5AXQBsD0NDPbEFczK98hk/aa6HJxbl+UtLkJV2+Bvy5hLSLk3LHmqt0NTkKNso1A9y1a4w==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/sunos-x64@0.25.3':
    resolution: {integrity: sha512-ROJhm7d8bk9dMCUZjkS8fgzsPAZEjtRJqCAmVgB0gMrvG7hfmPmz9k1rwO4jSiblFjYmNvbECL9uhaPzONMfgA==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.25.3':
    resolution: {integrity: sha512-YWcow8peiHpNBiIXHwaswPnAXLsLVygFwCB3A7Bh5jRkIBFWHGmNQ48AlX4xDvQNoMZlPYzjVOQDYEzWCqufMQ==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.25.3':
    resolution: {integrity: sha512-qspTZOIGoXVS4DpNqUYUs9UxVb04khS1Degaw/MnfMe7goQ3lTfQ13Vw4qY/Nj0979BGvMRpAYbs/BAxEvU8ew==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.25.3':
    resolution: {integrity: sha512-ICgUR+kPimx0vvRzf+N/7L7tVSQeE3BYY+NhHRHXS1kBuPO7z2+7ea2HbhDyZdTephgvNvKrlDDKUexuCVBVvg==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [win32]

  '@floating-ui/core@1.6.9':
    resolution: {integrity: sha512-uMXCuQ3BItDUbAMhIXw7UPXRfAlOAvZzdK9BWpE60MCn+Svt3aLn9jsPTi/WNGlRUu2uI0v5S7JiIUsbsvh3fw==}

  '@floating-ui/devtools@0.2.1':
    resolution: {integrity: sha512-8PHJLbD6VhBh+LJ1uty/Bz30qs02NXCE5u8WpOhSewlYXUWl03GNXknr9AS2yaAWJEQaY27x7eByJs44gODBcw==}
    peerDependencies:
      '@floating-ui/dom': '>=1.5.4'

  '@floating-ui/dom@1.6.13':
    resolution: {integrity: sha512-umqzocjDgNRGTuO7Q8CU32dkHkECqI8ZdMZ5Swb6QAM0t5rnlrN3lGo1hdpscRd3WS8T6DKYK4ephgIH9iRh3w==}

  '@floating-ui/utils@0.2.9':
    resolution: {integrity: sha512-MDWhGtE+eHw5JW7lq4qhc5yRLS11ERl1c7Z6Xd0a58DozHES6EnNNwUWbMiG4J9Cgj053Bhk8zvlhFYKVhULwg==}

  '@fluentui-contrib/houdini-utils@0.3.4':
    resolution: {integrity: sha512-aN7kvMxpmkbExOE1Tij4nVNyHb4Y5dm37QHu8VlEJPVkbkUW6YVoRQev/SeJpKPR4W/A1eod+wTcHrxCjPsn1w==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.8.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui-copilot/chat-input-plugins@0.4.2':
    resolution: {integrity: sha512-YBXIAzYPYqeI5918uidq6NjVuqiubAylYkRpkReWE9qBaHV6mpNyFFRfFeRgaNybTrOf1hPwPBl05tArl7dAkg==}

  '@fluentui-copilot/flair@0.4.10':
    resolution: {integrity: sha512-cHZslh2VmbEj9GdwSJnhas/YiwXhZnoT0eAVgfbaAxCV2HKg77K9+ezk5Tkg4+BIBh2QU7pc748/qoLNLilVZg==}

  '@fluentui-copilot/react-announce@0.5.7':
    resolution: {integrity: sha512-455T1Z1sHz8tgQrj9Zl1EUnNKfBMrdxfFolNTvVdF8RbvTQ0FREBWlWT9syPRMwHBjpZqxKHRkwS1M5VluxZXA==}
    peerDependencies:
      '@fluentui/react-components': '>=9.58.3 <10.0.0'
      '@fluentui/react-context-selector': '>=9.1.76 <10.0.0'
      '@fluentui/react-shared-contexts': '>=9.23.1 <10.0.0'
      '@fluentui/react-utilities': '>=9.19.0 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-attachments@0.10.7':
    resolution: {integrity: sha512-BP5EX46UYTDK7dsFvLWnzWGob9/Yd/3XX2eGUqsHx4nuz8aM0L55XB+r0gzejDpUfwN4xdD1DwyQa29mOintBg==}
    peerDependencies:
      '@fluentui/keyboard-keys': '>=9.0.7 <10.0.0'
      '@fluentui/react-components': '>=9.55.1 <10.0.0'
      '@fluentui/react-context-selector': '>=9.1.68 <10.0.0'
      '@fluentui/react-icons': '>=2.0.260 <3.0.0'
      '@fluentui/react-jsx-runtime': '>=9.0.45 <10.0.0'
      '@fluentui/react-shared-contexts': '>=9.20.2 <10.0.0'
      '@fluentui/react-utilities': '>=9.18.16 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-chat-input-plugins@0.4.8':
    resolution: {integrity: sha512-ODxx9GqqyI+uZCMxq/OFEt6XJiz8Y4RX+fmomB0VX+B3MOOaicuMwZu7YEqvZJCs1wkKw19McKsfS8Lu8Bg9lQ==}
    peerDependencies:
      '@fluentui/react-components': '>=9.58.3 <10.0.0'
      '@fluentui/react-jsx-runtime': '>=9.0.50 <10.0.0'
      '@fluentui/react-utilities': '>=9.19.0 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-chat-input@0.11.3':
    resolution: {integrity: sha512-uoxglBQ4sYv0eQISjbyUPNjNdLorhCu+ss7lphX5pbFb+XEHVZpIv7a1uD0pK+MI9PXLpYGahjnQQBw8lJgeFA==}
    peerDependencies:
      '@fluentui/keyboard-keys': '>=9.0.7 <10.0.0'
      '@fluentui/react-components': '>=9.55.1 <10.0.0'
      '@fluentui/react-context-selector': '>=9.1.68 <10.0.0'
      '@fluentui/react-icons': '>=2.0.260 <3.0.0'
      '@fluentui/react-jsx-runtime': '>=9.0.45 <10.0.0'
      '@fluentui/react-motion': '>=9.6.0 <10.0.0'
      '@fluentui/react-shared-contexts': '>=9.20.2 <10.0.0'
      '@fluentui/react-utilities': '>=9.18.16 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-copilot-chat@0.9.6':
    resolution: {integrity: sha512-jvKXwq1TUxL0Cd/F6uEUrRoDjVI2LRFmeqx2KAT5lTF3SWVjzAcsVyhmoqwk97BNs5dJ6sP3qZjuHd8vmwLx8A==}
    peerDependencies:
      '@fluentui/react-components': '>=9.55.1 <10.0.0'
      '@fluentui/react-context-selector': '>=9.1.68 <10.0.0'
      '@fluentui/react-icons': '>=2.0.260 <3.0.0'
      '@fluentui/react-jsx-runtime': '>=9.0.45 <10.0.0'
      '@fluentui/react-shared-contexts': '>=9.20.2 <10.0.0'
      '@fluentui/react-tabster': '>=9.22.9 <10.0.0'
      '@fluentui/react-utilities': '>=9.18.16 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-copilot@0.23.3':
    resolution: {integrity: sha512-Y4o9ZhFZdnFju0PG9jAChyqc6dk7HibqWVLMhAqv+bN1TLEiChSuusDvugW0nLxRkZ4v9xq99utMSJNbosnyiQ==}
    peerDependencies:
      '@fluentui/keyboard-keys': '>=9.0.7 <10.0.0'
      '@fluentui/react-aria': '>=9.13.2 <10.0.0'
      '@fluentui/react-components': '>=9.55.1 <10.0.0'
      '@fluentui/react-context-selector': '>=9.1.68 <10.0.0'
      '@fluentui/react-icons': '>=2.0.260 <3.0.0'
      '@fluentui/react-jsx-runtime': '>=9.0.45 <10.0.0'
      '@fluentui/react-motion': '>=9.6.0 <10.0.0'
      '@fluentui/react-shared-contexts': '>=9.20.2 <10.0.0'
      '@fluentui/react-tabster': '>=9.22.9 <10.0.0'
      '@fluentui/react-utilities': '>=9.18.16 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-editor-input@0.4.9':
    resolution: {integrity: sha512-hAm/1RinIPiUX0r+Xs/LCklCWkNXMbJ4XHkOO6hrDuzvO5AWYZ1Z17T5Pl3b4FgDQwxKeya33uBcB7u4YM1eSQ==}
    peerDependencies:
      '@fluentui/react-components': '>=9.58.3 <10.0.0'
      '@fluentui/react-jsx-runtime': '>=9.0.50 <10.0.0'
      '@fluentui/react-utilities': '>=9.19.0 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-entity-cards@0.1.4':
    resolution: {integrity: sha512-+BZfvwXTeZloZiDizcu1ldXgFK9xmOYslJczEaPocpz21bpDv5heV5596TEsYWHs9dP5+6sxSGngq3TWDmR78g==}
    peerDependencies:
      '@fluentui/react-components': '>=9.55.1 <10.0.0'
      '@fluentui/react-context-selector': '>=9.1.68 <10.0.0'
      '@fluentui/react-icons': '>=2.0.260 <3.0.0'
      '@fluentui/react-jsx-runtime': '>=9.0.45 <10.0.0'
      '@fluentui/react-utilities': '>=9.18.16 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-feedback-buttons@0.9.6':
    resolution: {integrity: sha512-31GOfTH/r8RjedIwjhyg0iOOvquW5Yu5DwKrwgBgYiNgfolzDOeRcMNfAjnlTOEo6jFFCUvUOTc1AfDU5aOqDQ==}
    peerDependencies:
      '@fluentui/react-components': '>=9.55.1 <10.0.0'
      '@fluentui/react-context-selector': '>=9.1.68 <10.0.0'
      '@fluentui/react-icons': '>=2.0.260 <3.0.0'
      '@fluentui/react-jsx-runtime': '>=9.0.45 <10.0.0'
      '@fluentui/react-shared-contexts': '>=9.20.2 <10.0.0'
      '@fluentui/react-utilities': '>=9.18.16 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-first-run-experience@0.6.4':
    resolution: {integrity: sha512-qs/6bFZ/lOyGgHJHTmns1dp5UArNhIulOxkok5Yk2NFFztkpmXG7KvWiY5zk+EJsTY2c7zC/KA0uC9M5ik9dUA==}
    peerDependencies:
      '@fluentui/react-components': '>=9.55.1 <10.0.0'
      '@fluentui/react-context-selector': '>=9.1.68 <10.0.0'
      '@fluentui/react-jsx-runtime': '>=9.0.45 <10.0.0'
      '@fluentui/react-utilities': '>=9.18.16 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-flair@0.5.13':
    resolution: {integrity: sha512-AbLfVmF1QKBlPI5objAhEPqxFk/8GTaS/41J2qq6HoqwDfwakXCLPTLL9PL+m0MfkNUiyzga/u1ZmuVns7I3Kg==}
    peerDependencies:
      '@fluentui/react-components': '>=9.58.3 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-input-listbox@0.1.3':
    resolution: {integrity: sha512-LMLYbWxl1AIvJRhT387deKyw2a2KInHlWOWfc6tHYiYCUyCWq0C4FlrBOmJzkB/AJwH7Z6GrvA+wO/u5DTLy8g==}
    peerDependencies:
      '@fluentui/keyboard-keys': '>=9.0.7 <10.0.0'
      '@fluentui/react-aria': '>=9.13.2 <10.0.0'
      '@fluentui/react-components': '>=9.55.1 <10.0.0'
      '@fluentui/react-context-selector': '>=9.1.68 <10.0.0'
      '@fluentui/react-icons': '>=2.0.260 <3.0.0'
      '@fluentui/react-jsx-runtime': '>=9.0.45 <10.0.0'
      '@fluentui/react-positioning': '>=9.15.7 <10.0.0'
      '@fluentui/react-shared-contexts': '>=9.20.2 <10.0.0'
      '@fluentui/react-tabster': '>=9.22.9 <10.0.0'
      '@fluentui/react-utilities': '>=9.18.16 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-latency@0.8.5':
    resolution: {integrity: sha512-jvUMnU512SP60555OypGJF0CrnA+I1b41vEgvDA6NvsFKJ/76CFbaPEaF8zk0lMesSAEdnpnTzEPyZXLpMolvg==}
    peerDependencies:
      '@fluentui/react-components': '>=9.55.1 <10.0.0'
      '@fluentui/react-context-selector': '>=9.1.68 <10.0.0'
      '@fluentui/react-icons': '>=2.0.260 <3.0.0'
      '@fluentui/react-jsx-runtime': '>=9.0.45 <10.0.0'
      '@fluentui/react-shared-contexts': '>=9.20.2 <10.0.0'
      '@fluentui/react-utilities': '>=9.18.16 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-output-card@0.9.5':
    resolution: {integrity: sha512-KfQdpP/1XzS8bHaCv/9OspqUi/LyXCXQl/YqNQ9fTjdCFzGzJ/3bJlIOlof8Kt7lvxyu4Lz9j14X/BVNGcS68g==}
    peerDependencies:
      '@fluentui/react-components': '>=9.55.1 <10.0.0'
      '@fluentui/react-context-selector': '>=9.1.68 <10.0.0'
      '@fluentui/react-jsx-runtime': '>=9.0.45 <10.0.0'
      '@fluentui/react-shared-contexts': '>=9.20.2 <10.0.0'
      '@fluentui/react-utilities': '>=9.18.16 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-preview@0.5.8':
    resolution: {integrity: sha512-wdY5ZNfhiR5hcJfieWwFtRR9QUiRBin88yhAcTMAyUBD50T5cmUISWvYky7gdW/wK4iANZmmYoaRzo3eooWhJA==}
    peerDependencies:
      '@fluentui/keyboard-keys': '>=9.0.7 <10.0.0'
      '@fluentui/react-components': '>=9.55.1 <10.0.0'
      '@fluentui/react-context-selector': '>=9.1.68 <10.0.0'
      '@fluentui/react-jsx-runtime': '>=9.0.45 <10.0.0'
      '@fluentui/react-utilities': '>=9.18.16 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-prompt-input@0.5.4':
    resolution: {integrity: sha512-Kuu4VcfCAsq5IqqLlSaEpmT/xwMKJjwD1vIoV9dRANMQWKlegJe4tzJl2KB9vatR8B0PbVBZZjmaM3gU7SKJHw==}
    peerDependencies:
      '@fluentui/keyboard-keys': '>=9.0.7 <10.0.0'
      '@fluentui/react-components': '>=9.55.1 <10.0.0'
      '@fluentui/react-icons': '>=2.0.260 <3.0.0'
      '@fluentui/react-jsx-runtime': '>=9.0.45 <10.0.0'
      '@fluentui/react-shared-contexts': '>=9.20.2 <10.0.0'
      '@fluentui/react-tabster': '>=9.22.9 <10.0.0'
      '@fluentui/react-utilities': '>=9.18.16 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-prompt-listbox@0.5.2':
    resolution: {integrity: sha512-MN2zmn2EJ8clcIGAE+ZnW4ynN/X5uQvaPxFFHhfQvynFCXkP5KkJ4LQwiBezqlCfakBsgzPb+ZAClYgZiL/cpw==}
    peerDependencies:
      '@fluentui/keyboard-keys': '>=9.0.7 <10.0.0'
      '@fluentui/react-aria': '>=9.13.2 <10.0.0'
      '@fluentui/react-combobox': '>=9.13.3 <10.0.0'
      '@fluentui/react-components': '>=9.55.1 <10.0.0'
      '@fluentui/react-icons': '>=2.0.260 <3.0.0'
      '@fluentui/react-jsx-runtime': '>=9.0.45 <10.0.0'
      '@fluentui/react-positioning': '>=9.15.7 <10.0.0'
      '@fluentui/react-shared-contexts': '>=9.20.2 <10.0.0'
      '@fluentui/react-tabster': '>=9.22.9 <10.0.0'
      '@fluentui/react-utilities': '>=9.18.16 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-prompt-starter@0.7.5':
    resolution: {integrity: sha512-tikKDKaRcWvUHnh1nslwD6AAhoQwkKOOdKesxZakqLUv2Vi1vNtpCcpX1uN6cNs94CVy9NOvU9yCreJ2xBzJyQ==}
    peerDependencies:
      '@fluentui/react-components': '>=9.55.1 <10.0.0'
      '@fluentui/react-context-selector': '>=9.1.68 <10.0.0'
      '@fluentui/react-jsx-runtime': '>=9.0.45 <10.0.0'
      '@fluentui/react-shared-contexts': '>=9.20.2 <10.0.0'
      '@fluentui/react-utilities': '>=9.18.16 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-provider@0.9.4':
    resolution: {integrity: sha512-ya46k1silEe9QRZZsrPuGX9eAejs2wFatGTfWWvvmaKnfQTLo6B+5sDrHXsM87X+bm7i8o7592QHGx5/NQ24TA==}
    peerDependencies:
      '@fluentui/react-components': '>=9.55.1 <10.0.0'
      '@fluentui/react-context-selector': '>=9.1.68 <10.0.0'
      '@fluentui/react-shared-contexts': '>=9.20.2 <10.0.0'
      '@fluentui/react-utilities': '>=9.18.16 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-reference@0.13.10':
    resolution: {integrity: sha512-a9rw34GG6XPJV0HxcEHE3S5ChiWT32K8nqz6qVTT7G0uzuvcEu6y76XldCqljk7cYZgClkE0H7p6PAc4T/lVfw==}
    peerDependencies:
      '@fluentui/keyboard-keys': '>=9.0.7 <10.0.0'
      '@fluentui/react-components': '>=9.55.1 <10.0.0'
      '@fluentui/react-context-selector': '>=9.1.68 <10.0.0'
      '@fluentui/react-icons': '>=2.0.260 <3.0.0'
      '@fluentui/react-jsx-runtime': '>=9.0.45 <10.0.0'
      '@fluentui/react-shared-contexts': '>=9.20.2 <10.0.0'
      '@fluentui/react-utilities': '>=9.18.16 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-response-count@0.2.13':
    resolution: {integrity: sha512-PbHdGP5b8pxx8gxmTVJ/An/B31HY+jjb4Sb5ATt0DEI3+TUAecoK++N8Lc0wxc30GmilcfGVgWh/NiA8n04pLg==}
    peerDependencies:
      '@fluentui/react-components': '>=9.58.3 <10.0.0'
      '@fluentui/react-jsx-runtime': '>=9.0.50 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-sensitivity-label@0.5.8':
    resolution: {integrity: sha512-IoPIEGhtOGFuywuIjJijoWEJD7mWwk2bHW/5/k5b9AWdVo8kS0uWPbOA0NRD43Dtn4WCzaQF8Fb9g/vbvzsYNw==}
    peerDependencies:
      '@fluentui/keyboard-keys': '>=9.0.7 <10.0.0'
      '@fluentui/react-components': '>=9.55.1 <10.0.0'
      '@fluentui/react-context-selector': '>=9.1.68 <10.0.0'
      '@fluentui/react-icons': '>=2.0.260 <3.0.0'
      '@fluentui/react-jsx-runtime': '>=9.0.45 <10.0.0'
      '@fluentui/react-utilities': '>=9.18.16 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-snippet@0.2.14':
    resolution: {integrity: sha512-n+e2+CUuN0p0Y1RB44bhVwfAvmd9yYEeQWNq+eOPwO59NlidkJHLBNoFiIYiVyh41p+pC9VVQxBeEOFfxkciCA==}
    peerDependencies:
      '@fluentui/react-components': '>=9.58.3 <10.0.0'
      '@fluentui/react-jsx-runtime': '>=9.0.50 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-suggestions@0.10.1':
    resolution: {integrity: sha512-M4f4KnYjTs3OEGY8BLYsYy3rEkGbkN+Qi2oK/8mUZIijchsuEwpwmCxoidav4fmmtI1zb7IwLI6QasAyq5mmNA==}
    peerDependencies:
      '@fluentui/react-components': '>=9.55.1 <10.0.0'
      '@fluentui/react-context-selector': '>=9.1.68 <10.0.0'
      '@fluentui/react-icons': '>=2.0.260 <3.0.0'
      '@fluentui/react-jsx-runtime': '>=9.0.45 <10.0.0'
      '@fluentui/react-shared-contexts': '>=9.20.2 <10.0.0'
      '@fluentui/react-utilities': '>=9.18.16 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-text-editor@0.4.1':
    resolution: {integrity: sha512-BsyXjJY/+NHd+0mN4yEx3P0vyyR05bcpStScVyA6ATZXCm4obK/MJ0ROCgBaoJsJgcXQTfWHOTdpHjpc0z6pmw==}

  '@fluentui-copilot/react-textarea@0.8.5':
    resolution: {integrity: sha512-4XqER3mpHLjUBPT7ZYlOnmrfflgWnrDz8pmLWhsmDqvurJ1+NPOu0C0p5NrAlCcgjzQLoHbOiZOZyfaTmWHMxg==}
    peerDependencies:
      '@fluentui/keyboard-keys': '>=9.0.7 <10.0.0'
      '@fluentui/react-components': '>=9.55.1 <10.0.0'
      '@fluentui/react-context-selector': '>=9.1.68 <10.0.0'
      '@fluentui/react-icons': '>=2.0.260 <3.0.0'
      '@fluentui/react-jsx-runtime': '>=9.0.45 <10.0.0'
      '@fluentui/react-shared-contexts': '>=9.20.2 <10.0.0'
      '@fluentui/react-utilities': '>=9.18.16 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/react-utilities@0.0.5':
    resolution: {integrity: sha512-8x5qfbpBraYjMOfYwjIY0elIxqKptKYT4lky3WPvTJ/Bib5MyMY9PCzx5e9njTDyPBI200RqJBGfn8PnbEV7dw==}
    peerDependencies:
      '@fluentui/react-components': '>=9.55.1 <10.0.0'
      '@fluentui/react-jsx-runtime': '>=9.0.45 <10.0.0'
      '@fluentui/react-utilities': '>=9.18.16 <10.0.0'
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.8 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui-copilot/text-editor@0.3.1':
    resolution: {integrity: sha512-oCLVd9WodfoS8V/F2Rhr93QRV9Ojp2m70EDdFzZ6sxNd7NULDetDIpF1RUbcrdqWCCuRC3g/irZYLPt7nW0S5Q==}

  '@fluentui-copilot/tokens@0.3.10':
    resolution: {integrity: sha512-XUAtojrm7UPfVxr5oBgUWo0Oidj7qTwe097lpQ7kInfjSGgLvo0Faip/63v2jVaFOLx59960GJtuwbUFeuv25Q==}

  '@fluentui/keyboard-keys@9.0.8':
    resolution: {integrity: sha512-iUSJUUHAyTosnXK8O2Ilbfxma+ZyZPMua5vB028Ys96z80v+LFwntoehlFsdH3rMuPsA8GaC1RE7LMezwPBPdw==}

  '@fluentui/priority-overflow@9.1.15':
    resolution: {integrity: sha512-/3jPBBq64hRdA416grVj+ZeMBUIaKZk2S5HiRg7CKCAV1JuyF84Do0rQI6ns8Vb9XOGuc4kurMcL/UEftoEVrg==}

  '@fluentui/react-accordion@9.6.8':
    resolution: {integrity: sha512-KE3YNGPTsN7tbnAfgbs7THAFfURj+yvO2GgEIVr++2xTgHXR7GEwM7RrbE1ZbJZM7gbeGE5rCg006OBfE4qbuQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-alert@9.0.0-beta.124':
    resolution: {integrity: sha512-yFBo3B5H9hnoaXxlkuz8wRz04DEyQ+ElYA/p5p+Vojf19Zuta8DmFZZ6JtWdtxcdnnQ4LvAfC5OYYlzdReozPA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-aria@9.14.6':
    resolution: {integrity: sha512-3vaEzujXdQxhYFzRXnkDNDImbMS0FXa8pq9WPo0JiKThsQp1QQQzdQbFsY7vfHd9aWjjWyCrRDMH25H37ZZ06w==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-avatar@9.7.6':
    resolution: {integrity: sha512-T4W+CaGjdkWgfiI7Me+wtFj2ewZFH+GpZbSexatqDjoq5ywrFOZf+aADtaHuocHcH7hx/U3AXMvTTQyzoDMuRg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-badge@9.2.54':
    resolution: {integrity: sha512-2PU0UA0VDz/XwbYKmMmPQKg4ykYHoUsgs3oZIqdwMPM3zxuhclsFEFx2xj4nxpMKiGCTBSBTM0fdOEQwRrbluQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-breadcrumb@9.1.6':
    resolution: {integrity: sha512-61oH9e/6kBRhnDeSStWe0AMS/9I9nkSzTFEKHEWJDnZYPFIhvlzn56TcAtuzVUYgHV1Jsk5PRLd9aQtJL1ENYw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-button@9.4.6':
    resolution: {integrity: sha512-1G92nGpWOYQ7vR+3g0Y0RLeAlqpZnpHVhXpQG504+yDGIAsn76I1zt+XcD9/2uaDmoH4tXiHS1SPtShujYVXjA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-card@9.2.5':
    resolution: {integrity: sha512-ODgwhKt+GL0TbLLRb3kHuf4ftCdcat3uNdN6mcVrqsL3+elJONvd7OA02jFbU/WFVR3IKOx6dUjRYNQYol0CiA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-carousel@9.6.6':
    resolution: {integrity: sha512-/08DyIdg+wn72D+ShnOUQXHqMgAsFUIlCMmyBvNlMImFIDxE6NjVXy+5Yes5mpsJYGWetmCAcECf+SQdgDsU1w==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-checkbox@9.3.6':
    resolution: {integrity: sha512-70HiPwnR5Ed59bulKs733xTFtm9JHQCJlaJc+l9LR6jpiZucvMqNGDfpYDqvFTbCm+FCrvo6gxmKfADAHxANWw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-combobox@9.14.6':
    resolution: {integrity: sha512-JGkc5wW+NNlMs1P+UIMLWCzYux5SMgFMLjXuXEFP52hX8ka9Nk7l8WSTW58LRsccT5Mf6JXDiSs4CK5D6VXBOA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-components@9.60.0':
    resolution: {integrity: sha512-rio7BAgF5oyQ0jL6/h1By5ya6pBZtf1UFj0YOto75OhLp/PdHqZy5vWpntveUnih6hwnGeZ07EQkgyPnZl5Rog==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-context-selector@9.1.76':
    resolution: {integrity: sha512-GmkHiLuMBzYOVvPkXNhMJTusx9hf43+VizFjAhSfZWOnNwLjiekjDocs7S2XD0f3MmcVx+aB2tRdTDHxGAF/1A==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'
      scheduler: '>=0.19.0 <=0.23.0'

  '@fluentui/react-dialog@9.12.8':
    resolution: {integrity: sha512-bfZehsH5ejXc8qO5SZdu50siusz3VhpP1imCVSz92cwsyowjOaGX8DAjfbvb+QfNT/0RYqqdveCXTLPVC7SFWQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-divider@9.2.86':
    resolution: {integrity: sha512-8hzwDVdW7CkumW8XU16lsrrg6s0tNAIWdsFC4Utfb/BL2xgfJRdg/0q6Dzw12uhQHtssC3pKNQV0mp4ia0oqww==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-drawer@9.7.8':
    resolution: {integrity: sha512-s9epUHmw/MrkVEpjzZJcdwjYSx2dVM7Uf9deHA//+dgHCM0Yybmn6vWJ+OArNkzdpYHLBuG3Cwk07tnQqGJx2Q==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-field@9.2.6':
    resolution: {integrity: sha512-C+x+96pRgpx1ib08krazPdYn7+lgD7kDGNlvadmUPM05Zm68Zm2mytMjZ/iy50N/iydozmbLG/i930LVUAF7/g==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-icons@2.0.279':
    resolution: {integrity: sha512-117W2i9zgJ+S9MpnjE7EtXwYRNC6J3u4El47lQPkc4m1qZolXa8xDYvwo14CiqO0pe3Eh1jFSCfyshMNvASaeA==}
    peerDependencies:
      react: '>=16.8.0 <19.0.0'

  '@fluentui/react-image@9.1.84':
    resolution: {integrity: sha512-+8X9IPtNi+RLsSJEIODUfnnalPXLJpfqSyyjrVcm/xjEasCm77F1kMSzCGiHbFYvz7hq5g5I4B/OH4TjL+fcqg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-infobutton@9.0.0-beta.102':
    resolution: {integrity: sha512-3kA4F0Vga8Ds6JGlBajLCCDOo/LmPuS786Wg7ui4ZTDYVIMzy1yp2XuVcZniifBFvEp0HQCUoDPWUV0VI3FfzQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-infolabel@9.2.0':
    resolution: {integrity: sha512-lNxcGj2kcpykdoOW9HSielq7o30RI2vI5LTy4pgd5OQ7/1ffik6+ioKPjylnIV6nIPgv035x4Pf/8s9B6m0+oA==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/react-input@9.5.6':
    resolution: {integrity: sha512-qegsdTawoewipYPYKfuYm21p5VZ59Yl33ofQiGoUgY03r/ddylHenWIOLdFyuUuPXBv2m/ESMCL1MZaiDUPDFg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-jsx-runtime@9.0.54':
    resolution: {integrity: sha512-zSkP9X/bAFg17QUDBs4bnbDUgeQSpSBVbH4nKYa3cZb78vV3e3m3nyADBvb97NYkywyd7CfIXq8iTpDWVEoWTw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'

  '@fluentui/react-label@9.1.87':
    resolution: {integrity: sha512-vfUppmSWkpwXztHU21oGcduYQ9jldkPrFpl+/zWmbiOia5CKTMqJtHqLJMMe/W1uoNKqoNU37uVp3bZgIWUHJg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-link@9.4.6':
    resolution: {integrity: sha512-PuOyp8JObLWzvUsK8PKjqITtwdcRxonEUxOztvv3HPAyE11EtgdvPKEhm5cQPmXN/EA/D2/Dk80PHLeNRzVaZQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-list@9.1.6':
    resolution: {integrity: sha512-Sgl0wVQnJrKFRh4AERtB3eyJERDkSBhH4dGz+KaPaltItWe/0g2/VOqNSOk9oaG3rka7BYSeU6pLUf5AwkqMgA==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/react-menu@9.16.6':
    resolution: {integrity: sha512-qiVoje/i8Pj0joZN/uaJd6r0H9qZTgjAEgsnJc32HEXQuX3HDGkxdxiCES+h24a8Alu09ELwVJAzdbWvedCqug==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-message-bar@9.4.7':
    resolution: {integrity: sha512-9XevlyC5Kr7oVBMo8Dd9ddw8Fmgq/yLN19zz+jyHzPGZMWU+BC40LM/w8l11WdgqN6ij5LZewq9pElMRMJIKkw==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/react-motion-components-preview@0.4.9':
    resolution: {integrity: sha512-sMtCqgmPHclfo6EqeIZmtXqJt+1fJn0Bo7ORsayXRJvjrmf8buDFnCJCjzYPNUR3npy9GFedMqmIkC6ovKkV0w==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-motion@9.7.2':
    resolution: {integrity: sha512-xUDkTNPsXKZIlk+Xr+uozdEmKfZ3iNE7dXUAPOgX5rntdMS50JZf4ggyaKdSJsuOVQNqWAoEcCNYLISroDw07g==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/react-overflow@9.3.6':
    resolution: {integrity: sha512-yyYX+6jLDyWwZg2G3r4gTxziaT70U9pdRUO1oEVE6Sv1xvMsQGfRQth4khl6OihB1fAFv4mAyx7NTX94D8RYhw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-persona@9.3.6':
    resolution: {integrity: sha512-EJZk6ZANrWoZ+lGvjW+xXuj5AGu5uNT6LiBo1H3SM6ug/eldz32Pa+UXYPU6sVEW0T8+wUCvPZmhgMnahELSew==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-popover@9.10.6':
    resolution: {integrity: sha512-ddYAbytBGukB2EgcjcMV7q7A8Yh6tmWk8Eg9m1O0rAnB/8xlkuG8BLAN98S4kAGsmrxX2GMb1R3NBBFr+yMdGA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-portal@9.5.6':
    resolution: {integrity: sha512-mAZ5sVf+2TiG5BKOo7Vv88+UeBZEeVGnTZcI6U2iggB7LLzPQdl3Bw+w8QUMBO9PHS/QtzliTqeGpt2QbtMyxw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-positioning@9.16.7':
    resolution: {integrity: sha512-31i2VdDegR5NsHiQxPP7pWQz4u8lkQq9T1rUFHUUtT7OLr3vOcKf0dGWIeMfZ3LzIv+aCX/P3d2bwEiydCeXuA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-progress@9.2.6':
    resolution: {integrity: sha512-I3zTci64PskUGMS/2tnR3nw5hKsBKu5S7PEiCySUoy+fGSFLKGWaUC0G68EdmFCIwa4AIHG66dUyyTfr/Hjfzg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-provider@9.20.6':
    resolution: {integrity: sha512-yxMWLP1SZMKVLSuE4Z4617DpbzcvItjOvQl/MOiaQHdC6zfq6X+1Ud4thH49o42KIUoIeWsCBSsEyaDAdYBUvg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-radio@9.3.6':
    resolution: {integrity: sha512-cOZzd8lN1NCVwKnkepTi9B58mEJdds2wJH8veqLv9cbNceD4Bju53xTr2UG9nIXugwdV85tptYU/o0a4oakIRA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-rating@9.1.6':
    resolution: {integrity: sha512-uFyp+/iY9Y4ORdtljYBRDfDBZL/wdCmEHwFyNFXKSdFPePeZhkCaKZq7RH+6KCGHY4KTFqDer4IaBgxBaC2oHg==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/react-search@9.1.6':
    resolution: {integrity: sha512-3iu+axBpXuSg2wywOVj9mCqwWEiAtIseLQVFTvY8/q93/fXkHWwXEV1pgfTzPlXNWy19CLV+cuXF0v4D2+JmDA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-select@9.2.6':
    resolution: {integrity: sha512-Yeb/EGOhNrCAseTj6eBgJ0QtWiyibloXepYbyZ3QryXhPeZBLR32yhKVvzGB+ScB4hdY45k/Tam8BdfofstAqw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-shared-contexts@9.23.1':
    resolution: {integrity: sha512-mP+7talxLz7n0G36o7Asdvst+JPzUbqbnoMKUWRVB5YwzlOXumEgaQDgL1BkRUJYaDGOjIiSTUjHOEkBt7iSdg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'

  '@fluentui/react-skeleton@9.2.6':
    resolution: {integrity: sha512-qAb0Td07EqCmyJAK53TYDfWs3NWedqAC7YRt2RVfhaQobI60+etMkXhGDwGDZwryQIFMYlNm6mSV4M5qkC4gCA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-slider@9.3.7':
    resolution: {integrity: sha512-PY4Z9KujrxyRZaLgdY47BlGj3LCmIiCRJE/96DSDz7iPbwfVluH0HJbBsw+MfW70c0CwyPD5VSHtPIi0pnGnKw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-spinbutton@9.3.6':
    resolution: {integrity: sha512-XVK1AOjKS47MBEKDDKzgePc3DfIr2f1LI+OgmbcAlhBUgyy2FGeixqdAvbJTnRehO6kRRzFjSmMLnb6c6m/W/w==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-spinner@9.5.11':
    resolution: {integrity: sha512-q0mJLG7LfWSRqa2fO+Qvxw/noZWjk3HM4wurbddTOClezTcBlMXlYlad7rueu9TpzM5caGsWcMF791/gNYLHmQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-swatch-picker@9.2.6':
    resolution: {integrity: sha512-s9rGkiONRxc8lmc19vbKgrkGtFzKCRv1+Cov5esIG/uJnvmTctzOLjgFj+NeWehvQgrtv8t7Bs7AszlQzfEP5A==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/react-switch@9.2.6':
    resolution: {integrity: sha512-stFoqh/ahYmY3LPVIi3voGMPm/wcMMEepkWL8ZLYU5ZKP/knJ2Yy5peW1uVo+5d6PbLUvan9tsSB53IN/2utpA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-table@9.16.6':
    resolution: {integrity: sha512-u/skqMkdw16Lnje4CevcU1xoSspwTWRLoHXvIiWQyjSkd/mHkspflNJy/wK2aoEO5F7pPak0u72IBxMg+0KIvQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-tabs@9.7.6':
    resolution: {integrity: sha512-N8wey1p/bGnHNZd8L/AVU7GOiI3bodbAlL9x9L8grncPMX/WWnwTGMui7A3Ge3u2IQ3rR8XEXz/dVxFpTdv+dg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-tabster@9.24.6':
    resolution: {integrity: sha512-d0i4Yey8UE+zf+dM/wYtblRwRhxuE9uWdwsxWD5tdvDY3KZxIa9NsNW7xBRA1Az5dhvWw83fJJBd88DosX2sYw==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-tag-picker@9.5.6':
    resolution: {integrity: sha512-DO65MbrWXz7YFc44TSCLGaowtnnje6UMqczCYrQVwzmQlxf00RqgbB3CVjKvW0Z3r89aNLtX9b+mYOQL4ForVg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-tags@9.5.4':
    resolution: {integrity: sha512-xmrhhmNa/hwW4p6gTjsFbctcohsiBJS96SfA/cQQ/pRpNKpjwiAZvppF3R4dBYo1Apnt9VCdAmEYhu7qmjq69A==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-teaching-popover@9.4.5':
    resolution: {integrity: sha512-axKj4EQuoDsGSK0sEdOAEuwg3ew7Maxu4xKF2Z2jOOf0J7+6lKiZilTzt3gf5XLHUzFMU2bTM7VVAN8O8Et04A==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  '@fluentui/react-text@9.4.36':
    resolution: {integrity: sha512-oLSGz6uksooCQrc+FXvWwAZCP+ucn2h12vZFyWSAOVODDtQMjtycol03p408BEHnPBQbrYaQCFpd3Id5eLuxBg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-textarea@9.4.6':
    resolution: {integrity: sha512-zdpeCSnVJihbPXHeAYHa1MiO7dJba3Ugtyu7TqJkmiy0Lr5OfeTkX2nLchOPKiEDjQFfSviqDNYZERwO2NGD1g==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-theme@9.1.24':
    resolution: {integrity: sha512-OhVKYD7CMYHxzJEn4PtIszledj8hbQJNWBMfIZsp4Sytdp9vCi0txIQUx4BhS1WqtQPhNGCF16eW9Q3NRrnIrQ==}

  '@fluentui/react-toast@9.4.8':
    resolution: {integrity: sha512-1welldVf/M/c7msCwB8a0yFgKjIF/aUxAgjTHza9jEmxBl45oCzPZY7PVApCY2sSx+iRn8XjSKRkYSPgHUYzKA==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-toolbar@9.4.5':
    resolution: {integrity: sha512-/Za5QHVqcF1bLW7FIRebl17TI+MCdoVqvHoaE7xodRmAA0a5MWcs3aqtumaeQjZBnGh9HsFYmxTKdh0KEu4LVg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-tooltip@9.6.6':
    resolution: {integrity: sha512-4EHxH5CvzPQjOjl9opldAhSAVSOoUo4ei412RoCRASzoaVBJwQ81r2MaVlf9P84G6WOUUXttRbUQ+0jWV5WoKg==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-tree@9.10.9':
    resolution: {integrity: sha512-Pj/eSXVVw3kGae7Jl3ZBaRqjSOm9JytzgA13eYdHpx58YpqGsYOU6G5CFwx28pTQKeIZIfsIgTDPhD+S5LcVOQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/react-utilities@9.19.0':
    resolution: {integrity: sha512-66Kdpr4xZsov6KSqbPDmKR5CB96RUPZuWihMC3RYHj9uH+oxd81k2Jyrb6rM058xjVKDFSFVLUZlsp1Mgts38w==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'

  '@fluentui/react-virtualizer@9.0.0-alpha.92':
    resolution: {integrity: sha512-hT7xwMXyXco/tQRdZgPZS3O8DjJnYIKc9EnZ2qQMYrv0PEiee01WwH2NVFbhkn2utD9TZlE3Gu0j25fSYICBxQ==}
    peerDependencies:
      '@types/react': '>=16.14.0 <19.0.0'
      '@types/react-dom': '>=16.9.0 <19.0.0'
      react: '>=16.14.0 <19.0.0'
      react-dom: '>=16.14.0 <19.0.0'

  '@fluentui/tokens@1.0.0-alpha.21':
    resolution: {integrity: sha512-xQ1T56sNgDFGl+kJdIwhz67mHng8vcwO7Dvx5Uja4t+NRULQBgMcJ4reUo4FGF3TjufHj08pP0/OnKQgnOaSVg==}

  '@griffel/core@1.19.2':
    resolution: {integrity: sha512-WkB/QQkjy9dE4vrNYGhQvRRUHFkYVOuaznVOMNTDT4pS9aTJ9XPrMTXXlkpcwaf0D3vNKoerj4zAwnU2lBzbOg==}

  '@griffel/react@1.5.30':
    resolution: {integrity: sha512-1q4ojbEVFY5YA0j1NamP0WWF4BKh+GHsVugltDYeEgEaVbH3odJ7tJabuhQgY+7Nhka0pyEFWSiHJev0K3FSew==}
    peerDependencies:
      react: '>=16.8.0 <20.0.0'

  '@griffel/style-types@1.3.0':
    resolution: {integrity: sha512-bHwD3sUE84Xwv4dH011gOKe1jul77M1S6ZFN9Tnq8pvZ48UMdY//vtES6fv7GRS5wXYT4iqxQPBluAiYAfkpmw==}

  '@jridgewell/gen-mapping@0.3.8':
    resolution: {integrity: sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.2.1':
    resolution: {integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/sourcemap-codec@1.5.0':
    resolution: {integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==}

  '@jridgewell/trace-mapping@0.3.25':
    resolution: {integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==}

  '@lexical/clipboard@0.12.6':
    resolution: {integrity: sha512-rJFp7tXzawCrMWWRsjCR80dZoIkLJ/EPgPmTk3xqpc+9ntlwbkm3LUOdFmgN+pshnhiZTQBwbFqg/QbsA1Pw9g==}
    peerDependencies:
      lexical: 0.12.6

  '@lexical/code@0.12.6':
    resolution: {integrity: sha512-D0IBKLzDFfVqk+3KPlJd2gWIq+h5QOsVn5Atz/Eh2eLRpOakSsZiRjmddsijoLsZbvgo1HObRPQAoeATRPWIzg==}
    peerDependencies:
      lexical: 0.12.6

  '@lexical/dragon@0.12.6':
    resolution: {integrity: sha512-VKbXzdtF6qizwESx7Zag/VGiYKMAc+xpJF7tcwv5SH8I4bnseoozafzxRG6AE7J9nzGwO74ypKqPmmpP9e20BA==}
    peerDependencies:
      lexical: 0.12.6

  '@lexical/hashtag@0.12.6':
    resolution: {integrity: sha512-SiEId/IBIqUKJJKGg8HSumalfKGxtZQJRkF7Q50FqFSU906V1lcM1jkU7aVw0hiuEHg3H+vFBmNTRdXKyoibsw==}
    peerDependencies:
      lexical: 0.12.6

  '@lexical/headless@0.12.6':
    resolution: {integrity: sha512-FDyN9FRvWqWUrMp3vzQAH+KngYLxwAfSjinhf40D+r4Y5OxgU7rpYBBBeQ5n6H+Zr1YvkIRFnTmOdqlKUcm3/g==}
    peerDependencies:
      lexical: 0.12.6

  '@lexical/history@0.12.6':
    resolution: {integrity: sha512-3vvbUF6XHuk/9985IQIXP15g+nr7SlwsPrd2IteOg6aNF+HeE2ttJS5dOiSJLnVZm+AX0OMgejMC1uU2uiZOtA==}
    peerDependencies:
      lexical: 0.12.6

  '@lexical/html@0.12.6':
    resolution: {integrity: sha512-HVlJLCkazLbLpxdw0mwMkteQuv6OMkJTlAi6qGJimtuqJLm45BpaQ16PTpUmFWpWeIHL2XpvcDX/lj5fm68XPA==}
    peerDependencies:
      lexical: 0.12.6

  '@lexical/link@0.12.6':
    resolution: {integrity: sha512-mrFFWR0EZ9liRUzHZqb2ijUDZqkCM+bNsyYqLh4I1CrJpzQtakyIEJt/GzYz4IHmmsRqwcc2zXUP/4kENjjPlQ==}
    peerDependencies:
      lexical: 0.12.6

  '@lexical/list@0.12.6':
    resolution: {integrity: sha512-9DFe8vpSxZ8NQZ/67ZFNiRptB3XPa7mUl0Rmd5WpbJHJHmiORyngYkYgKOW56T/TCtYcLfkTbctMhsIt8OeIqQ==}
    peerDependencies:
      lexical: 0.12.6

  '@lexical/mark@0.12.6':
    resolution: {integrity: sha512-utk6kgTSTuzmM0+B4imGTGwC4gQRCQ4hHEZTVbkIDbONvjbo9g6xfbTO9g6Qxs2h7Zt0Q2eDA7RG4nwC3vN1KQ==}
    peerDependencies:
      lexical: 0.12.6

  '@lexical/markdown@0.12.6':
    resolution: {integrity: sha512-q1cQ4w6KYxUF1N6nGwJTZwn8szLo0kbr8DzI62samZTxeztA0ByMSZLzvO5LSGhgeDremuWx5oa97s2qJMQZFw==}
    peerDependencies:
      lexical: 0.12.6

  '@lexical/offset@0.12.6':
    resolution: {integrity: sha512-5NgIaWCvMuOQNf3SZSNn459QfsN7SmLl+Tu4ISqxyZRoMV5Sfojzion9MjCVmt1YSsIS4B29NYQvGQ/li1saOw==}
    peerDependencies:
      lexical: 0.12.6

  '@lexical/overflow@0.12.6':
    resolution: {integrity: sha512-4TZJhTGkn7xvR+rumSYW9U/OIsbith0kVGOvZZf+DM/t9fb0IVQWWSWmMlXJ5XNt/qXLFof3HFyJhK84dsN3NA==}
    peerDependencies:
      lexical: 0.12.6

  '@lexical/plain-text@0.12.6':
    resolution: {integrity: sha512-YF+EaWGQIxR1SHgeSuPrrqqSK8RYDxGv9RYyuIPvWXpt3M9NWw7hyAn7zxmXGgv2BhIicyHGPy5CyQgt3Mkb/w==}
    peerDependencies:
      '@lexical/clipboard': 0.12.6
      '@lexical/selection': 0.12.6
      '@lexical/utils': 0.12.6
      lexical: 0.12.6

  '@lexical/react@0.12.6':
    resolution: {integrity: sha512-Pto4wsVwrnY94tzcCXP2kWukQejSRPDfwOPd+EFh8dUzj+L7fa9Pze2wVgCRZpEohwfbcgAdEsvmSbhz+yGkog==}
    peerDependencies:
      lexical: 0.12.6
      react: '>=17.x'
      react-dom: '>=17.x'

  '@lexical/rich-text@0.12.6':
    resolution: {integrity: sha512-fRZHy2ug6Pq+pJK7trr9phTGaD2ba3If8o36dphOsl27MtUllpz68lcXL6mUonzJhAu4um1e9u7GFR3dLp+cVA==}
    peerDependencies:
      '@lexical/clipboard': 0.12.6
      '@lexical/selection': 0.12.6
      '@lexical/utils': 0.12.6
      lexical: 0.12.6

  '@lexical/selection@0.12.6':
    resolution: {integrity: sha512-HJBEazVwOe6duyHV6+vB/MS4kUBlCV05Cfcigdx8HlLLFQRWPqHrTpaxKz4jfb9ar0SlI2W1BUNbySAxMkC/HQ==}
    peerDependencies:
      lexical: 0.12.6

  '@lexical/table@0.12.6':
    resolution: {integrity: sha512-rUh9/fN831T6UpNiPuzx0x6HNi/eQ7W5AQrVBwwzEwkbwAqnE0n28DP924AUbX72UsQNHtywgmDApMoEV7W2iQ==}
    peerDependencies:
      lexical: 0.12.6

  '@lexical/text@0.12.6':
    resolution: {integrity: sha512-WfqfH9gvPAx9Hi9wrJDWECdvt6turPQXImCRI657LVfsP2hHh4eHpcSnd3YYH313pv98HPWmeMstBbEieYwTpQ==}
    peerDependencies:
      lexical: 0.12.6

  '@lexical/utils@0.12.6':
    resolution: {integrity: sha512-hK5r/TD2nH5TfWSiCxy7/lh0s11qJcI1wo++PBQOR9o937pQ+/Zr/1tMOc8MnrTpl89mtmYtPfWW3f++HH1Yog==}
    peerDependencies:
      lexical: 0.12.6

  '@lexical/yjs@0.12.6':
    resolution: {integrity: sha512-I/Yf/Qm8/ydU983kWpFBlDFNFQXLYur5uaAimTSBcJuqHmy3cv1xM7Xrq4BtM+0orKgWJt8vR8cLVIU9sAmzfw==}
    peerDependencies:
      lexical: 0.12.6
      yjs: '>=13.5.22'

  '@rollup/rollup-android-arm-eabi@4.40.0':
    resolution: {integrity: sha512-+Fbls/diZ0RDerhE8kyC6hjADCXA1K4yVNlH0EYfd2XjyH0UGgzaQ8MlT0pCXAThfxv3QUAczHaL+qSv1E4/Cg==}
    cpu: [arm]
    os: [android]

  '@rollup/rollup-android-arm64@4.40.0':
    resolution: {integrity: sha512-PPA6aEEsTPRz+/4xxAmaoWDqh67N7wFbgFUJGMnanCFs0TV99M0M8QhhaSCks+n6EbQoFvLQgYOGXxlMGQe/6w==}
    cpu: [arm64]
    os: [android]

  '@rollup/rollup-darwin-arm64@4.40.0':
    resolution: {integrity: sha512-GwYOcOakYHdfnjjKwqpTGgn5a6cUX7+Ra2HeNj/GdXvO2VJOOXCiYYlRFU4CubFM67EhbmzLOmACKEfvp3J1kQ==}
    cpu: [arm64]
    os: [darwin]

  '@rollup/rollup-darwin-x64@4.40.0':
    resolution: {integrity: sha512-CoLEGJ+2eheqD9KBSxmma6ld01czS52Iw0e2qMZNpPDlf7Z9mj8xmMemxEucinev4LgHalDPczMyxzbq+Q+EtA==}
    cpu: [x64]
    os: [darwin]

  '@rollup/rollup-freebsd-arm64@4.40.0':
    resolution: {integrity: sha512-r7yGiS4HN/kibvESzmrOB/PxKMhPTlz+FcGvoUIKYoTyGd5toHp48g1uZy1o1xQvybwwpqpe010JrcGG2s5nkg==}
    cpu: [arm64]
    os: [freebsd]

  '@rollup/rollup-freebsd-x64@4.40.0':
    resolution: {integrity: sha512-mVDxzlf0oLzV3oZOr0SMJ0lSDd3xC4CmnWJ8Val8isp9jRGl5Dq//LLDSPFrasS7pSm6m5xAcKaw3sHXhBjoRw==}
    cpu: [x64]
    os: [freebsd]

  '@rollup/rollup-linux-arm-gnueabihf@4.40.0':
    resolution: {integrity: sha512-y/qUMOpJxBMy8xCXD++jeu8t7kzjlOCkoxxajL58G62PJGBZVl/Gwpm7JK9+YvlB701rcQTzjUZ1JgUoPTnoQA==}
    cpu: [arm]
    os: [linux]

  '@rollup/rollup-linux-arm-musleabihf@4.40.0':
    resolution: {integrity: sha512-GoCsPibtVdJFPv/BOIvBKO/XmwZLwaNWdyD8TKlXuqp0veo2sHE+A/vpMQ5iSArRUz/uaoj4h5S6Pn0+PdhRjg==}
    cpu: [arm]
    os: [linux]

  '@rollup/rollup-linux-arm64-gnu@4.40.0':
    resolution: {integrity: sha512-L5ZLphTjjAD9leJzSLI7rr8fNqJMlGDKlazW2tX4IUF9P7R5TMQPElpH82Q7eNIDQnQlAyiNVfRPfP2vM5Avvg==}
    cpu: [arm64]
    os: [linux]

  '@rollup/rollup-linux-arm64-musl@4.40.0':
    resolution: {integrity: sha512-ATZvCRGCDtv1Y4gpDIXsS+wfFeFuLwVxyUBSLawjgXK2tRE6fnsQEkE4csQQYWlBlsFztRzCnBvWVfcae/1qxQ==}
    cpu: [arm64]
    os: [linux]

  '@rollup/rollup-linux-loongarch64-gnu@4.40.0':
    resolution: {integrity: sha512-wG9e2XtIhd++QugU5MD9i7OnpaVb08ji3P1y/hNbxrQ3sYEelKJOq1UJ5dXczeo6Hj2rfDEL5GdtkMSVLa/AOg==}
    cpu: [loong64]
    os: [linux]

  '@rollup/rollup-linux-powerpc64le-gnu@4.40.0':
    resolution: {integrity: sha512-vgXfWmj0f3jAUvC7TZSU/m/cOE558ILWDzS7jBhiCAFpY2WEBn5jqgbqvmzlMjtp8KlLcBlXVD2mkTSEQE6Ixw==}
    cpu: [ppc64]
    os: [linux]

  '@rollup/rollup-linux-riscv64-gnu@4.40.0':
    resolution: {integrity: sha512-uJkYTugqtPZBS3Z136arevt/FsKTF/J9dEMTX/cwR7lsAW4bShzI2R0pJVw+hcBTWF4dxVckYh72Hk3/hWNKvA==}
    cpu: [riscv64]
    os: [linux]

  '@rollup/rollup-linux-riscv64-musl@4.40.0':
    resolution: {integrity: sha512-rKmSj6EXQRnhSkE22+WvrqOqRtk733x3p5sWpZilhmjnkHkpeCgWsFFo0dGnUGeA+OZjRl3+VYq+HyCOEuwcxQ==}
    cpu: [riscv64]
    os: [linux]

  '@rollup/rollup-linux-s390x-gnu@4.40.0':
    resolution: {integrity: sha512-SpnYlAfKPOoVsQqmTFJ0usx0z84bzGOS9anAC0AZ3rdSo3snecihbhFTlJZ8XMwzqAcodjFU4+/SM311dqE5Sw==}
    cpu: [s390x]
    os: [linux]

  '@rollup/rollup-linux-x64-gnu@4.40.0':
    resolution: {integrity: sha512-RcDGMtqF9EFN8i2RYN2W+64CdHruJ5rPqrlYw+cgM3uOVPSsnAQps7cpjXe9be/yDp8UC7VLoCoKC8J3Kn2FkQ==}
    cpu: [x64]
    os: [linux]

  '@rollup/rollup-linux-x64-musl@4.40.0':
    resolution: {integrity: sha512-HZvjpiUmSNx5zFgwtQAV1GaGazT2RWvqeDi0hV+AtC8unqqDSsaFjPxfsO6qPtKRRg25SisACWnJ37Yio8ttaw==}
    cpu: [x64]
    os: [linux]

  '@rollup/rollup-win32-arm64-msvc@4.40.0':
    resolution: {integrity: sha512-UtZQQI5k/b8d7d3i9AZmA/t+Q4tk3hOC0tMOMSq2GlMYOfxbesxG4mJSeDp0EHs30N9bsfwUvs3zF4v/RzOeTQ==}
    cpu: [arm64]
    os: [win32]

  '@rollup/rollup-win32-ia32-msvc@4.40.0':
    resolution: {integrity: sha512-+m03kvI2f5syIqHXCZLPVYplP8pQch9JHyXKZ3AGMKlg8dCyr2PKHjwRLiW53LTrN/Nc3EqHOKxUxzoSPdKddA==}
    cpu: [ia32]
    os: [win32]

  '@rollup/rollup-win32-x64-msvc@4.40.0':
    resolution: {integrity: sha512-lpPE1cLfP5oPzVjKMx10pgBmKELQnFJXHgvtHCtuJWOv8MxqdEIMNtgHgBFf7Ea2/7EuVwa9fodWUfXAlXZLZQ==}
    cpu: [x64]
    os: [win32]

  '@swc/helpers@0.5.17':
    resolution: {integrity: sha512-5IKx/Y13RsYd+sauPb2x+U/xZikHjolzfuDgTAl/Tdf3Q8rslRvC19NKDLgAJQ6wsqADk10ntlv08nPFw/gO/A==}

  '@types/babel__core@7.20.5':
    resolution: {integrity: sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==}

  '@types/babel__generator@7.27.0':
    resolution: {integrity: sha512-ufFd2Xi92OAVPYsy+P4n7/U7e68fex0+Ee8gSG9KX7eo084CWiQ4sdxktvdl0bOPupXtVJPY19zk6EwWqUQ8lg==}

  '@types/babel__template@7.4.4':
    resolution: {integrity: sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==}

  '@types/babel__traverse@7.20.7':
    resolution: {integrity: sha512-dkO5fhS7+/oos4ciWxyEyjWe48zmG6wbCheo/G2ZnHx4fs3EU6YC6UM8rk56gAjNJ9P3MTH2jo5jb92/K6wbng==}

  '@types/debug@4.1.12':
    resolution: {integrity: sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==}

  '@types/estree-jsx@1.0.5':
    resolution: {integrity: sha512-52CcUVNFyfb1A2ALocQw/Dd1BQFNmSdkuC3BkZ6iqhdMfQz7JWOFRuJFloOzjk+6WijU56m9oKXFAXc7o3Towg==}

  '@types/estree@1.0.7':
    resolution: {integrity: sha512-w28IoSUCJpidD/TGviZwwMJckNESJZXFu7NBZ5YJ4mEUnNraUn9Pm8HSZm/jDF1pDWYKspWE7oVphigUPRakIQ==}

  '@types/hast@2.3.10':
    resolution: {integrity: sha512-McWspRw8xx8J9HurkVBfYj0xKoE25tOFlHGdx4MJ5xORQrMGZNqJhVQWaIbm6Oyla5kYOXtDiopzKRJzEOkwJw==}

  '@types/hast@3.0.4':
    resolution: {integrity: sha512-WPs+bbQw5aCj+x6laNGWLH3wviHtoCv/P3+otBhbOhJgG8qtpdAMlTCxLtsTWA7LH1Oh/bFCHsBn0TPS5m30EQ==}

  '@types/katex@0.16.7':
    resolution: {integrity: sha512-HMwFiRujE5PjrgwHQ25+bsLJgowjGjm5Z8FVSf0N6PwgJrwxH0QxzHYDcKsTfV3wva0vzrpqMTJS2jXPr5BMEQ==}

  '@types/mdast@4.0.4':
    resolution: {integrity: sha512-kGaNbPh1k7AFzgpud/gMdvIm5xuECykRR+JnWKQno9TAXVa6WIVCGTPvYGekIDL4uwCZQSYbUxNBSb1aUo79oA==}

  '@types/ms@2.1.0':
    resolution: {integrity: sha512-GsCCIZDE/p3i96vtEqx+7dBUGXrc7zeSK3wwPHIaRThS+9OhWIXRqzs4d6k1SVU8g91DrNRWxWUGhp5KXQb2VA==}

  '@types/node@22.14.1':
    resolution: {integrity: sha512-u0HuPQwe/dHrItgHHpmw3N2fYCR6x4ivMNbPHRkBVP4CvN+kiRrKHWk3i8tXiO/joPwXLMYvF9TTF0eqgHIuOw==}

  '@types/prop-types@15.7.14':
    resolution: {integrity: sha512-gNMvNH49DJ7OJYv+KAKn0Xp45p8PLl6zo2YnvDIbTd4J6MER2BmWN49TG7n9LvkyihINxeKW8+3bfS2yDC9dzQ==}

  '@types/react-dom@18.3.5':
    resolution: {integrity: sha512-P4t6saawp+b/dFrUr2cvkVsfvPguwsxtH6dNIYRllMsefqFzkZk5UIjzyDOv5g1dXIPdG4Sp1yCR4Z6RCUsG/Q==}
    peerDependencies:
      '@types/react': ^18.0.0

  '@types/react-syntax-highlighter@15.5.13':
    resolution: {integrity: sha512-uLGJ87j6Sz8UaBAooU0T6lWJ0dBmjZgN1PZTrj05TNql2/XpC6+4HhMT5syIdFUUt+FASfCeLLv4kBygNU+8qA==}

  '@types/react@18.3.20':
    resolution: {integrity: sha512-IPaCZN7PShZK/3t6Q87pfTkRm6oLTd4vztyoj+cbHUF1g3FfVb2tFIL79uCRKEfv16AhqDMBywP2VW3KIZUvcg==}

  '@types/unist@2.0.11':
    resolution: {integrity: sha512-CmBKiL6NNo/OqgmMn95Fk9Whlp2mtvIv+KNpQKN2F4SjvrEesubTRWGYSg+BnWZOnlCaSTU1sMpsBOzgbYhnsA==}

  '@types/unist@3.0.3':
    resolution: {integrity: sha512-ko/gIFJRv177XgZsZcBwnqJN5x/Gien8qNOn0D5bQU/zAzVf9Zt3BlcUiLqhV9y4ARk0GbT3tnUiPNgnTXzc/Q==}

  '@ungap/structured-clone@1.3.0':
    resolution: {integrity: sha512-WmoN8qaIAo7WTYWbAZuG8PYEhn5fkz7dZrqTBZ7dtt//lL2Gwms1IcnQ5yHqjDfX8Ft5j4YzDM23f87zBfDe9g==}

  '@vitejs/plugin-react@4.4.1':
    resolution: {integrity: sha512-IpEm5ZmeXAP/osiBXVVP5KjFMzbWOonMs0NaQQl+xYnUAcq4oHUBsF2+p4MgKWG4YMmFYJU8A6sxRPuowllm6w==}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      vite: ^4.2.0 || ^5.0.0 || ^6.0.0

  bail@2.0.2:
    resolution: {integrity: sha512-0xO6mYd7JB2YesxDKplafRpsiOzPt9V02ddPCLbY1xYGPOX24NTyN50qnUxgCPcSoYMhKpAuBTjQoRZCAkUDRw==}

  browserslist@4.24.4:
    resolution: {integrity: sha512-KDi1Ny1gSePi1vm0q4oxSF8b4DR44GF4BbmS2YdhPLOEqd8pDviZOGH/GsmRwoWJ2+5Lr085X7naowMwKHDG1A==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  caniuse-lite@1.0.30001715:
    resolution: {integrity: sha512-7ptkFGMm2OAOgvZpwgA4yjQ5SQbrNVGdRjzH0pBdy1Fasvcr+KAeECmbCAECzTuDuoX0FCY8KzUxjf9+9kfZEw==}

  ccount@2.0.1:
    resolution: {integrity: sha512-eyrF0jiFpY+3drT6383f1qhkbGsLSifNAjA61IUjZjmLCWjItY6LB9ft9YhoDgwfmclB2zhu51Lc7+95b8NRAg==}

  character-entities-html4@2.1.0:
    resolution: {integrity: sha512-1v7fgQRj6hnSwFpq1Eu0ynr/CDEw0rXo2B61qXrLNdHZmPKgb7fqS1a2JwF0rISo9q77jDI8VMEHoApn8qDoZA==}

  character-entities-legacy@1.1.4:
    resolution: {integrity: sha512-3Xnr+7ZFS1uxeiUDvV02wQ+QDbc55o97tIV5zHScSPJpcLm/r0DFPcoY3tYRp+VZukxuMeKgXYmsXQHO05zQeA==}

  character-entities-legacy@3.0.0:
    resolution: {integrity: sha512-RpPp0asT/6ufRm//AJVwpViZbGM/MkjQFxJccQRHmISF/22NBtsHqAWmL+/pmkPWoIUJdWyeVleTl1wydHATVQ==}

  character-entities@1.2.4:
    resolution: {integrity: sha512-iBMyeEHxfVnIakwOuDXpVkc54HijNgCyQB2w0VfGQThle6NXn50zU6V/u+LDhxHcDUPojn6Kpga3PTAD8W1bQw==}

  character-entities@2.0.2:
    resolution: {integrity: sha512-shx7oQ0Awen/BRIdkjkvz54PnEEI/EjwXDSIZp86/KKdbafHh1Df/RYGBhn4hbe2+uKC9FnT5UCEdyPz3ai9hQ==}

  character-reference-invalid@1.1.4:
    resolution: {integrity: sha512-mKKUkUbhPpQlCOfIuZkvSEgktjPFIsZKRRbC6KWVEMvlzblj3i3asQv5ODsrwt0N3pHAEvjP8KTQPHkp0+6jOg==}

  character-reference-invalid@2.0.1:
    resolution: {integrity: sha512-iBZ4F4wRbyORVsu0jPV7gXkOsGYjGHPmAyv+HiHG8gi5PtC9KI2j1+v8/tlibRvjoWX027ypmG/n0HtO5t7unw==}

  clsx@2.1.1:
    resolution: {integrity: sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==}
    engines: {node: '>=6'}

  comma-separated-tokens@1.0.8:
    resolution: {integrity: sha512-GHuDRO12Sypu2cV70d1dkA2EUmXHgntrzbpvOB+Qy+49ypNfGgFQIC2fhhXbnyrJRynDCAARsT7Ou0M6hirpfw==}

  comma-separated-tokens@2.0.3:
    resolution: {integrity: sha512-Fu4hJdvzeylCfQPp9SGWidpzrMs7tTrlu6Vb8XGaRGck8QSNZJJp538Wrb60Lax4fPwR64ViY468OIUTbRlGZg==}

  commander@8.3.0:
    resolution: {integrity: sha512-OkTL9umf+He2DZkUq8f8J9of7yL6RJKI24dVITBmNfZBmri9zYZQrKkuXiKhyfPSu8tUhnVBB1iKXevvnlR4Ww==}
    engines: {node: '>= 12'}

  convert-source-map@2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==}

  copy-to-clipboard@3.3.3:
    resolution: {integrity: sha512-2KV8NhB5JqC3ky0r9PMCAZKbUHSwtEo4CwCs0KXgruG43gX5PMqDEBbVU4OUzw2MuAWUfsuFmWvEKG5QRfSnJA==}

  csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  debug@4.4.0:
    resolution: {integrity: sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decode-named-character-reference@1.1.0:
    resolution: {integrity: sha512-Wy+JTSbFThEOXQIR2L6mxJvEs+veIzpmqD7ynWxMXGpnk3smkHQOp6forLdHsKpAMW9iJpaBBIxz285t1n1C3w==}

  dequal@2.0.3:
    resolution: {integrity: sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==}
    engines: {node: '>=6'}

  devlop@1.1.0:
    resolution: {integrity: sha512-RWmIqhcFf1lRYBvNmr7qTNuyCt/7/ns2jbpp1+PalgE/rDQcBT0fioSMUpJ93irlUhC5hrg4cYqe6U+0ImW0rA==}

  dom-helpers@5.2.1:
    resolution: {integrity: sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==}

  electron-to-chromium@1.5.142:
    resolution: {integrity: sha512-Ah2HgkTu/9RhTDNThBtzu2Wirdy4DC9b0sMT1pUhbkZQ5U/iwmE+PHZX1MpjD5IkJCc2wSghgGG/B04szAx07w==}

  embla-carousel-autoplay@8.6.0:
    resolution: {integrity: sha512-OBu5G3nwaSXkZCo1A6LTaFMZ8EpkYbwIaH+bPqdBnDGQ2fh4+NbzjXjs2SktoPNKCtflfVMc75njaDHOYXcrsA==}
    peerDependencies:
      embla-carousel: 8.6.0

  embla-carousel-fade@8.6.0:
    resolution: {integrity: sha512-qaYsx5mwCz72ZrjlsXgs1nKejSrW+UhkbOMwLgfRT7w2LtdEB03nPRI06GHuHv5ac2USvbEiX2/nAHctcDwvpg==}
    peerDependencies:
      embla-carousel: 8.6.0

  embla-carousel@8.6.0:
    resolution: {integrity: sha512-SjWyZBHJPbqxHOzckOfo8lHisEaJWmwd23XppYFYVh10bU66/Pn5tkVkbkCMZVdbUE5eTCI2nD8OyIP4Z+uwkA==}

  entities@6.0.0:
    resolution: {integrity: sha512-aKstq2TDOndCn4diEyp9Uq/Flu2i1GlLkc6XIDQSDMuaFE3OPW5OphLCyQ5SpSJZTb4reN+kTcYru5yIfXoRPw==}
    engines: {node: '>=0.12'}

  esbuild@0.25.3:
    resolution: {integrity: sha512-qKA6Pvai73+M2FtftpNKRxJ78GIjmFXFxd/1DVBqGo/qNhLSfv+G12n9pNoWdytJC8U00TrViOwpjT0zgqQS8Q==}
    engines: {node: '>=18'}
    hasBin: true

  escalade@3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==}
    engines: {node: '>=6'}

  escape-string-regexp@5.0.0:
    resolution: {integrity: sha512-/veY75JbMK4j1yjvuUxuVsiS/hr/4iHs9FTT6cgTexxdE0Ly/glccBAkloH/DofkjRbZU3bnoj38mOmhkZ0lHw==}
    engines: {node: '>=12'}

  estree-util-is-identifier-name@3.0.0:
    resolution: {integrity: sha512-hFtqIDZTIUZ9BXLb8y4pYGyk6+wekIivNVTcmvk8NoOh+VeRn5y6cEHzbURrWbfp1fIqdVipilzj+lfaadNZmg==}

  extend@3.0.2:
    resolution: {integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==}

  fault@1.0.4:
    resolution: {integrity: sha512-CJ0HCB5tL5fYTEA7ToAq5+kTwd++Borf1/bifxd9iT70QcXr4MRrO3Llf8Ifs70q+SJcGHFtnIE/Nw6giCtECA==}

  fdir@6.4.4:
    resolution: {integrity: sha512-1NZP+GK4GfuAv3PqKvxQRDMjdSRZjnkq7KfhlNrCNNlZ0ygQFpebfrnfnq/W7fpUnAv9aGWmY1zKx7FYL3gwhg==}
    peerDependencies:
      picomatch: ^3 || ^4
    peerDependenciesMeta:
      picomatch:
        optional: true

  format@0.2.2:
    resolution: {integrity: sha512-wzsgA6WOq+09wrU1tsJ09udeR/YZRaeArL9e1wPbFg3GG2yDnC2ldKpxs4xunpFF9DgqCqOIra3bc1HWrJ37Ww==}
    engines: {node: '>=0.4.x'}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}

  globals@11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}

  hast-util-from-dom@5.0.1:
    resolution: {integrity: sha512-N+LqofjR2zuzTjCPzyDUdSshy4Ma6li7p/c3pA78uTwzFgENbgbUrm2ugwsOdcjI1muO+o6Dgzp9p8WHtn/39Q==}

  hast-util-from-html-isomorphic@2.0.0:
    resolution: {integrity: sha512-zJfpXq44yff2hmE0XmwEOzdWin5xwH+QIhMLOScpX91e/NSGPsAzNCvLQDIEPyO2TXi+lBmU6hjLIhV8MwP2kw==}

  hast-util-from-html@2.0.3:
    resolution: {integrity: sha512-CUSRHXyKjzHov8yKsQjGOElXy/3EKpyX56ELnkHH34vDVw1N1XSQ1ZcAvTyAPtGqLTuKP/uxM+aLkSPqF/EtMw==}

  hast-util-from-parse5@8.0.3:
    resolution: {integrity: sha512-3kxEVkEKt0zvcZ3hCRYI8rqrgwtlIOFMWkbclACvjlDw8Li9S2hk/d51OI0nr/gIpdMHNepwgOKqZ/sy0Clpyg==}

  hast-util-is-element@3.0.0:
    resolution: {integrity: sha512-Val9mnv2IWpLbNPqc/pUem+a7Ipj2aHacCwgNfTiK0vJKl0LF+4Ba4+v1oPHFpf3bLYmreq0/l3Gud9S5OH42g==}

  hast-util-parse-selector@2.2.5:
    resolution: {integrity: sha512-7j6mrk/qqkSehsM92wQjdIgWM2/BW61u/53G6xmC8i1OmEdKLHbk419QKQUjz6LglWsfqoiHmyMRkP1BGjecNQ==}

  hast-util-parse-selector@4.0.0:
    resolution: {integrity: sha512-wkQCkSYoOGCRKERFWcxMVMOcYE2K1AaNLU8DXS9arxnLOUEWbOXKXiJUNzEpqZ3JOKpnha3jkFrumEjVliDe7A==}

  hast-util-raw@9.1.0:
    resolution: {integrity: sha512-Y8/SBAHkZGoNkpzqqfCldijcuUKh7/su31kEBp67cFY09Wy0mTRgtsLYsiIxMJxlu0f6AA5SUTbDR8K0rxnbUw==}

  hast-util-sanitize@5.0.2:
    resolution: {integrity: sha512-3yTWghByc50aGS7JlGhk61SPenfE/p1oaFeNwkOOyrscaOkMGrcW9+Cy/QAIOBpZxP1yqDIzFMR0+Np0i0+usg==}

  hast-util-to-html@9.0.5:
    resolution: {integrity: sha512-OguPdidb+fbHQSU4Q4ZiLKnzWo8Wwsf5bZfbvu7//a9oTYoqD/fWpe96NuHkoS9h0ccGOTe0C4NGXdtS0iObOw==}

  hast-util-to-jsx-runtime@2.3.6:
    resolution: {integrity: sha512-zl6s8LwNyo1P9uw+XJGvZtdFF1GdAkOg8ujOw+4Pyb76874fLps4ueHXDhXWdk6YHQ6OgUtinliG7RsYvCbbBg==}

  hast-util-to-parse5@8.0.0:
    resolution: {integrity: sha512-3KKrV5ZVI8if87DVSi1vDeByYrkGzg4mEfeu4alwgmmIeARiBLKCZS2uw5Gb6nU9x9Yufyj3iudm6i7nl52PFw==}

  hast-util-to-text@4.0.2:
    resolution: {integrity: sha512-KK6y/BN8lbaq654j7JgBydev7wuNMcID54lkRav1P0CaE1e47P72AWWPiGKXTJU271ooYzcvTAn/Zt0REnvc7A==}

  hast-util-whitespace@3.0.0:
    resolution: {integrity: sha512-88JUN06ipLwsnv+dVn+OIYOvAuvBMy/Qoi6O7mQHxdPXpjy+Cd6xRkWwux7DKO+4sYILtLBRIKgsdpS2gQc7qw==}

  hastscript@6.0.0:
    resolution: {integrity: sha512-nDM6bvd7lIqDUiYEiu5Sl/+6ReP0BMk/2f4U/Rooccxkj0P5nm+acM5PrGJ/t5I8qPGiqZSE6hVAwZEdZIvP4w==}

  hastscript@9.0.1:
    resolution: {integrity: sha512-g7df9rMFX/SPi34tyGCyUBREQoKkapwdY/T04Qn9TDWfHhAYt4/I0gMVirzK5wEzeUqIjEB+LXC/ypb7Aqno5w==}

  highlight.js@10.7.3:
    resolution: {integrity: sha512-tzcUFauisWKNHaRkN4Wjl/ZA07gENAjFl3J/c480dprkGTg5EQstgaNFqBfUqCq54kZRIEcreTsAgF/m2quD7A==}

  highlightjs-vue@1.0.0:
    resolution: {integrity: sha512-PDEfEF102G23vHmPhLyPboFCD+BkMGu+GuJe2d9/eH4FsCwvgBpnc9n0pGE+ffKdph38s6foEZiEjdgHdzp+IA==}

  html-url-attributes@3.0.1:
    resolution: {integrity: sha512-ol6UPyBWqsrO6EJySPz2O7ZSr856WDrEzM5zMqp+FJJLGMW35cLYmmZnl0vztAZxRUoNZJFTCohfjuIJ8I4QBQ==}

  html-void-elements@3.0.0:
    resolution: {integrity: sha512-bEqo66MRXsUGxWHV5IP0PUiAWwoEjba4VCzg0LjFJBpchPaTfyfCKTG6bc5F8ucKec3q5y6qOdGyYTSBEvhCrg==}

  inline-style-parser@0.2.4:
    resolution: {integrity: sha512-0aO8FkhNZlj/ZIbNi7Lxxr12obT7cL1moPfE4tg1LkX7LlLfC6DeX4l2ZEud1ukP9jNQyNnfzQVqwbwmAATY4Q==}

  is-alphabetical@1.0.4:
    resolution: {integrity: sha512-DwzsA04LQ10FHTZuL0/grVDk4rFoVH1pjAToYwBrHSxcrBIGQuXrQMtD5U1b0U2XVgKZCTLLP8u2Qxqhy3l2Vg==}

  is-alphabetical@2.0.1:
    resolution: {integrity: sha512-FWyyY60MeTNyeSRpkM2Iry0G9hpr7/9kD40mD/cGQEuilcZYS4okz8SN2Q6rLCJ8gbCt6fN+rC+6tMGS99LaxQ==}

  is-alphanumerical@1.0.4:
    resolution: {integrity: sha512-UzoZUr+XfVz3t3v4KyGEniVL9BDRoQtY7tOyrRybkVNjDFWyo1yhXNGrrBTQxp3ib9BLAWs7k2YKBQsFRkZG9A==}

  is-alphanumerical@2.0.1:
    resolution: {integrity: sha512-hmbYhX/9MUMF5uh7tOXyK/n0ZvWpad5caBA17GsC6vyuCqaWliRG5K1qS9inmUhEMaOBIW7/whAnSwveW/LtZw==}

  is-decimal@1.0.4:
    resolution: {integrity: sha512-RGdriMmQQvZ2aqaQq3awNA6dCGtKpiDFcOzrTWrDAT2MiWrKQVPmxLGHl7Y2nNu6led0kEyoX0enY0qXYsv9zw==}

  is-decimal@2.0.1:
    resolution: {integrity: sha512-AAB9hiomQs5DXWcRB1rqsxGUstbRroFOPPVAomNk/3XHR5JyEZChOyTWe2oayKnsSsr/kcGqF+z6yuH6HHpN0A==}

  is-hexadecimal@1.0.4:
    resolution: {integrity: sha512-gyPJuv83bHMpocVYoqof5VDiZveEoGoFL8m3BXNb2VW8Xs+rz9kqO8LOQ5DH6EsuvilT1ApazU0pyl+ytbPtlw==}

  is-hexadecimal@2.0.1:
    resolution: {integrity: sha512-DgZQp241c8oO6cA1SbTEWiXeoxV42vlcJxgH+B3hi1AiqqKruZR3ZGF8In3fj4+/y/7rHvlOZLZtgJ/4ttYGZg==}

  is-plain-obj@4.1.0:
    resolution: {integrity: sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg==}
    engines: {node: '>=12'}

  isomorphic.js@0.2.5:
    resolution: {integrity: sha512-PIeMbHqMt4DnUP3MA/Flc0HElYjMXArsw1qwJZcm9sqR8mq3l8NYizFMty0pWwE/tzIGH3EKK5+jes5mAr85yw==}

  js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  jsesc@3.1.0:
    resolution: {integrity: sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==}
    engines: {node: '>=6'}
    hasBin: true

  json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true

  katex@0.16.22:
    resolution: {integrity: sha512-XCHRdUw4lf3SKBaJe4EvgqIuWwkPSo9XoeO8GjQW94Bp7TWv9hNhzZjZ+OH9yf1UmLygb7DIT5GSFQiyt16zYg==}
    hasBin: true

  keyborg@2.6.0:
    resolution: {integrity: sha512-o5kvLbuTF+o326CMVYpjlaykxqYP9DphFQZ2ZpgrvBouyvOxyEB7oqe8nOLFpiV5VCtz0D3pt8gXQYWpLpBnmA==}

  lexical@0.12.6:
    resolution: {integrity: sha512-Nlfjc+k9cIWpOMv7XufF0Mv09TAXSemNAuAqFLaOwTcN+RvhvYTDtVLSp9D9r+5I097fYs1Vf/UYwH2xEpkFfQ==}

  lib0@0.2.105:
    resolution: {integrity: sha512-5vtbuBi2P43ZYOfVMV+TZYkWEa0J9kijXirzEgrPA+nJDQCtMx805/rqW4G1nXbM9IRIhwW+OyNNgcQdbhKfSw==}
    engines: {node: '>=16'}
    hasBin: true

  longest-streak@3.1.0:
    resolution: {integrity: sha512-9Ri+o0JYgehTaVBBDoMqIl8GXtbWg711O3srftcHhZ0dqnETqLaoIK0x17fUw9rFSlK/0NlsKe0Ahhyl5pXE2g==}

  loose-envify@1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true

  lowlight@1.20.0:
    resolution: {integrity: sha512-8Ktj+prEb1RoCPkEOrPMYUN/nCggB7qAWe3a7OpMjWQkh3l2RD5wKRQ+o8Q8YuI9RG/xs95waaI/E6ym/7NsTw==}

  lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}

  markdown-table@3.0.4:
    resolution: {integrity: sha512-wiYz4+JrLyb/DqW2hkFJxP7Vd7JuTDm77fvbM8VfEQdmSMqcImWeeRbHwZjBjIFki/VaMK2BhFi7oUUZeM5bqw==}

  mdast-util-find-and-replace@3.0.2:
    resolution: {integrity: sha512-Tmd1Vg/m3Xz43afeNxDIhWRtFZgM2VLyaf4vSTYwudTyeuTneoL3qtWMA5jeLyz/O1vDJmmV4QuScFCA2tBPwg==}

  mdast-util-from-markdown@2.0.2:
    resolution: {integrity: sha512-uZhTV/8NBuw0WHkPTrCqDOl0zVe1BIng5ZtHoDk49ME1qqcjYmmLmOf0gELgcRMxN4w2iuIeVso5/6QymSrgmA==}

  mdast-util-gfm-autolink-literal@2.0.1:
    resolution: {integrity: sha512-5HVP2MKaP6L+G6YaxPNjuL0BPrq9orG3TsrZ9YXbA3vDw/ACI4MEsnoDpn6ZNm7GnZgtAcONJyPhOP8tNJQavQ==}

  mdast-util-gfm-footnote@2.1.0:
    resolution: {integrity: sha512-sqpDWlsHn7Ac9GNZQMeUzPQSMzR6Wv0WKRNvQRg0KqHh02fpTz69Qc1QSseNX29bhz1ROIyNyxExfawVKTm1GQ==}

  mdast-util-gfm-strikethrough@2.0.0:
    resolution: {integrity: sha512-mKKb915TF+OC5ptj5bJ7WFRPdYtuHv0yTRxK2tJvi+BDqbkiG7h7u/9SI89nRAYcmap2xHQL9D+QG/6wSrTtXg==}

  mdast-util-gfm-table@2.0.0:
    resolution: {integrity: sha512-78UEvebzz/rJIxLvE7ZtDd/vIQ0RHv+3Mh5DR96p7cS7HsBhYIICDBCu8csTNWNO6tBWfqXPWekRuj2FNOGOZg==}

  mdast-util-gfm-task-list-item@2.0.0:
    resolution: {integrity: sha512-IrtvNvjxC1o06taBAVJznEnkiHxLFTzgonUdy8hzFVeDun0uTjxxrRGVaNFqkU1wJR3RBPEfsxmU6jDWPofrTQ==}

  mdast-util-gfm@3.1.0:
    resolution: {integrity: sha512-0ulfdQOM3ysHhCJ1p06l0b0VKlhU0wuQs3thxZQagjcjPrlFRqY215uZGHHJan9GEAXd9MbfPjFJz+qMkVR6zQ==}

  mdast-util-math@3.0.0:
    resolution: {integrity: sha512-Tl9GBNeG/AhJnQM221bJR2HPvLOSnLE/T9cJI9tlc6zwQk2nPk/4f0cHkOdEixQPC/j8UtKDdITswvLAy1OZ1w==}

  mdast-util-mdx-expression@2.0.1:
    resolution: {integrity: sha512-J6f+9hUp+ldTZqKRSg7Vw5V6MqjATc+3E4gf3CFNcuZNWD8XdyI6zQ8GqH7f8169MM6P7hMBRDVGnn7oHB9kXQ==}

  mdast-util-mdx-jsx@3.2.0:
    resolution: {integrity: sha512-lj/z8v0r6ZtsN/cGNNtemmmfoLAFZnjMbNyLzBafjzikOM+glrjNHPlf6lQDOTccj9n5b0PPihEBbhneMyGs1Q==}

  mdast-util-mdxjs-esm@2.0.1:
    resolution: {integrity: sha512-EcmOpxsZ96CvlP03NghtH1EsLtr0n9Tm4lPUJUBccV9RwUOneqSycg19n5HGzCf+10LozMRSObtVr3ee1WoHtg==}

  mdast-util-newline-to-break@2.0.0:
    resolution: {integrity: sha512-MbgeFca0hLYIEx/2zGsszCSEJJ1JSCdiY5xQxRcLDDGa8EPvlLPupJ4DSajbMPAnC0je8jfb9TiUATnxxrHUog==}

  mdast-util-phrasing@4.1.0:
    resolution: {integrity: sha512-TqICwyvJJpBwvGAMZjj4J2n0X8QWp21b9l0o7eXyVJ25YNWYbJDVIyD1bZXE6WtV6RmKJVYmQAKWa0zWOABz2w==}

  mdast-util-to-hast@13.2.0:
    resolution: {integrity: sha512-QGYKEuUsYT9ykKBCMOEDLsU5JRObWQusAolFMeko/tYPufNkRffBAQjIE+99jbA87xv6FgmjLtwjh9wBWajwAA==}

  mdast-util-to-markdown@2.1.2:
    resolution: {integrity: sha512-xj68wMTvGXVOKonmog6LwyJKrYXZPvlwabaryTjLh9LuvovB/KAH+kvi8Gjj+7rJjsFi23nkUxRQv1KqSroMqA==}

  mdast-util-to-string@4.0.0:
    resolution: {integrity: sha512-0H44vDimn51F0YwvxSJSm0eCDOJTRlmN0R1yBh4HLj9wiV1Dn0QoXGbvFAWj2hSItVTlCmBF1hqKlIyUBVFLPg==}

  micromark-core-commonmark@2.0.3:
    resolution: {integrity: sha512-RDBrHEMSxVFLg6xvnXmb1Ayr2WzLAWjeSATAoxwKYJV94TeNavgoIdA0a9ytzDSVzBy2YKFK+emCPOEibLeCrg==}

  micromark-extension-gfm-autolink-literal@2.1.0:
    resolution: {integrity: sha512-oOg7knzhicgQ3t4QCjCWgTmfNhvQbDDnJeVu9v81r7NltNCVmhPy1fJRX27pISafdjL+SVc4d3l48Gb6pbRypw==}

  micromark-extension-gfm-footnote@2.1.0:
    resolution: {integrity: sha512-/yPhxI1ntnDNsiHtzLKYnE3vf9JZ6cAisqVDauhp4CEHxlb4uoOTxOCJ+9s51bIB8U1N1FJ1RXOKTIlD5B/gqw==}

  micromark-extension-gfm-strikethrough@2.1.0:
    resolution: {integrity: sha512-ADVjpOOkjz1hhkZLlBiYA9cR2Anf8F4HqZUO6e5eDcPQd0Txw5fxLzzxnEkSkfnD0wziSGiv7sYhk/ktvbf1uw==}

  micromark-extension-gfm-table@2.1.1:
    resolution: {integrity: sha512-t2OU/dXXioARrC6yWfJ4hqB7rct14e8f7m0cbI5hUmDyyIlwv5vEtooptH8INkbLzOatzKuVbQmAYcbWoyz6Dg==}

  micromark-extension-gfm-tagfilter@2.0.0:
    resolution: {integrity: sha512-xHlTOmuCSotIA8TW1mDIM6X2O1SiX5P9IuDtqGonFhEK0qgRI4yeC6vMxEV2dgyr2TiD+2PQ10o+cOhdVAcwfg==}

  micromark-extension-gfm-task-list-item@2.1.0:
    resolution: {integrity: sha512-qIBZhqxqI6fjLDYFTBIa4eivDMnP+OZqsNwmQ3xNLE4Cxwc+zfQEfbs6tzAo2Hjq+bh6q5F+Z8/cksrLFYWQQw==}

  micromark-extension-gfm@3.0.0:
    resolution: {integrity: sha512-vsKArQsicm7t0z2GugkCKtZehqUm31oeGBV/KVSorWSy8ZlNAv7ytjFhvaryUiCUJYqs+NoE6AFhpQvBTM6Q4w==}

  micromark-extension-math@3.1.0:
    resolution: {integrity: sha512-lvEqd+fHjATVs+2v/8kg9i5Q0AP2k85H0WUOwpIVvUML8BapsMvh1XAogmQjOCsLpoKRCVQqEkQBB3NhVBcsOg==}

  micromark-factory-destination@2.0.1:
    resolution: {integrity: sha512-Xe6rDdJlkmbFRExpTOmRj9N3MaWmbAgdpSrBQvCFqhezUn4AHqJHbaEnfbVYYiexVSs//tqOdY/DxhjdCiJnIA==}

  micromark-factory-label@2.0.1:
    resolution: {integrity: sha512-VFMekyQExqIW7xIChcXn4ok29YE3rnuyveW3wZQWWqF4Nv9Wk5rgJ99KzPvHjkmPXF93FXIbBp6YdW3t71/7Vg==}

  micromark-factory-space@2.0.1:
    resolution: {integrity: sha512-zRkxjtBxxLd2Sc0d+fbnEunsTj46SWXgXciZmHq0kDYGnck/ZSGj9/wULTV95uoeYiK5hRXP2mJ98Uo4cq/LQg==}

  micromark-factory-title@2.0.1:
    resolution: {integrity: sha512-5bZ+3CjhAd9eChYTHsjy6TGxpOFSKgKKJPJxr293jTbfry2KDoWkhBb6TcPVB4NmzaPhMs1Frm9AZH7OD4Cjzw==}

  micromark-factory-whitespace@2.0.1:
    resolution: {integrity: sha512-Ob0nuZ3PKt/n0hORHyvoD9uZhr+Za8sFoP+OnMcnWK5lngSzALgQYKMr9RJVOWLqQYuyn6ulqGWSXdwf6F80lQ==}

  micromark-util-character@2.1.1:
    resolution: {integrity: sha512-wv8tdUTJ3thSFFFJKtpYKOYiGP2+v96Hvk4Tu8KpCAsTMs6yi+nVmGh1syvSCsaxz45J6Jbw+9DD6g97+NV67Q==}

  micromark-util-chunked@2.0.1:
    resolution: {integrity: sha512-QUNFEOPELfmvv+4xiNg2sRYeS/P84pTW0TCgP5zc9FpXetHY0ab7SxKyAQCNCc1eK0459uoLI1y5oO5Vc1dbhA==}

  micromark-util-classify-character@2.0.1:
    resolution: {integrity: sha512-K0kHzM6afW/MbeWYWLjoHQv1sgg2Q9EccHEDzSkxiP/EaagNzCm7T/WMKZ3rjMbvIpvBiZgwR3dKMygtA4mG1Q==}

  micromark-util-combine-extensions@2.0.1:
    resolution: {integrity: sha512-OnAnH8Ujmy59JcyZw8JSbK9cGpdVY44NKgSM7E9Eh7DiLS2E9RNQf0dONaGDzEG9yjEl5hcqeIsj4hfRkLH/Bg==}

  micromark-util-decode-numeric-character-reference@2.0.2:
    resolution: {integrity: sha512-ccUbYk6CwVdkmCQMyr64dXz42EfHGkPQlBj5p7YVGzq8I7CtjXZJrubAYezf7Rp+bjPseiROqe7G6foFd+lEuw==}

  micromark-util-decode-string@2.0.1:
    resolution: {integrity: sha512-nDV/77Fj6eH1ynwscYTOsbK7rR//Uj0bZXBwJZRfaLEJ1iGBR6kIfNmlNqaqJf649EP0F3NWNdeJi03elllNUQ==}

  micromark-util-encode@2.0.1:
    resolution: {integrity: sha512-c3cVx2y4KqUnwopcO9b/SCdo2O67LwJJ/UyqGfbigahfegL9myoEFoDYZgkT7f36T0bLrM9hZTAaAyH+PCAXjw==}

  micromark-util-html-tag-name@2.0.1:
    resolution: {integrity: sha512-2cNEiYDhCWKI+Gs9T0Tiysk136SnR13hhO8yW6BGNyhOC4qYFnwF1nKfD3HFAIXA5c45RrIG1ub11GiXeYd1xA==}

  micromark-util-normalize-identifier@2.0.1:
    resolution: {integrity: sha512-sxPqmo70LyARJs0w2UclACPUUEqltCkJ6PhKdMIDuJ3gSf/Q+/GIe3WKl0Ijb/GyH9lOpUkRAO2wp0GVkLvS9Q==}

  micromark-util-resolve-all@2.0.1:
    resolution: {integrity: sha512-VdQyxFWFT2/FGJgwQnJYbe1jjQoNTS4RjglmSjTUlpUMa95Htx9NHeYW4rGDJzbjvCsl9eLjMQwGeElsqmzcHg==}

  micromark-util-sanitize-uri@2.0.1:
    resolution: {integrity: sha512-9N9IomZ/YuGGZZmQec1MbgxtlgougxTodVwDzzEouPKo3qFWvymFHWcnDi2vzV1ff6kas9ucW+o3yzJK9YB1AQ==}

  micromark-util-subtokenize@2.1.0:
    resolution: {integrity: sha512-XQLu552iSctvnEcgXw6+Sx75GflAPNED1qx7eBJ+wydBb2KCbRZe+NwvIEEMM83uml1+2WSXpBAcp9IUCgCYWA==}

  micromark-util-symbol@2.0.1:
    resolution: {integrity: sha512-vs5t8Apaud9N28kgCrRUdEed4UJ+wWNvicHLPxCa9ENlYuAY31M0ETy5y1vA33YoNPDFTghEbnh6efaE8h4x0Q==}

  micromark-util-types@2.0.2:
    resolution: {integrity: sha512-Yw0ECSpJoViF1qTU4DC6NwtC4aWGt1EkzaQB8KPPyCRR8z9TWeV0HbEFGTO+ZY1wB22zmxnJqhPyTpOVCpeHTA==}

  micromark@4.0.2:
    resolution: {integrity: sha512-zpe98Q6kvavpCr1NPVSCMebCKfD7CA2NqZ+rykeNhONIJBpc1tFKt9hucLGwha3jNTNI8lHpctWJWoimVF4PfA==}

  ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  nanoid@3.3.11:
    resolution: {integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  node-releases@2.0.19:
    resolution: {integrity: sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==}

  object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  parse-entities@2.0.0:
    resolution: {integrity: sha512-kkywGpCcRYhqQIchaWqZ875wzpS/bMKhz5HnN3p7wveJTkTtyAB/AlnS0f8DFSqYW1T82t6yEAkEcB+A1I3MbQ==}

  parse-entities@4.0.2:
    resolution: {integrity: sha512-GG2AQYWoLgL877gQIKeRPGO1xF9+eG1ujIb5soS5gPvLQ1y2o8FL90w2QWNdf9I361Mpp7726c+lj3U0qK1uGw==}

  parse5@7.3.0:
    resolution: {integrity: sha512-IInvU7fabl34qmi9gY8XOVxhYyMyuH2xUNpb2q8/Y+7552KlejkRvqvD19nMoUW/uQGGbqNpA6Tufu5FL5BZgw==}

  picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  picomatch@4.0.2:
    resolution: {integrity: sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==}
    engines: {node: '>=12'}

  postcss@8.5.3:
    resolution: {integrity: sha512-dle9A3yYxlBSrt8Fu+IpjGT8SY8hN0mlaA6GY8t0P5PjIOZemULz/E2Bnm/2dcUOena75OTNkHI76uZBNUUq3A==}
    engines: {node: ^10 || ^12 || >=14}

  prismjs@1.30.0:
    resolution: {integrity: sha512-DEvV2ZF2r2/63V+tK8hQvrR2ZGn10srHbXviTlcv7Kpzw8jWiNTqbVgjO3IY8RxrrOUF8VPMQQFysYYYv0YZxw==}
    engines: {node: '>=6'}

  prop-types@15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==}

  property-information@5.6.0:
    resolution: {integrity: sha512-YUHSPk+A30YPv+0Qf8i9Mbfe/C0hdPXk1s1jPVToV8pk8BQtpw10ct89Eo7OWkutrwqvT0eicAxlOg3dOAu8JA==}

  property-information@6.5.0:
    resolution: {integrity: sha512-PgTgs/BlvHxOu8QuEN7wi5A0OmXaBcHpmCSTehcs6Uuu9IkDIEo13Hy7n898RHfrQ49vKCoGeWZSaAK01nwVig==}

  property-information@7.0.0:
    resolution: {integrity: sha512-7D/qOz/+Y4X/rzSB6jKxKUsQnphO046ei8qxG59mtM3RG3DHgTK81HrxrmoDVINJb8NKT5ZsRbwHvQ6B68Iyhg==}

  react-dom@19.1.0:
    resolution: {integrity: sha512-Xs1hdnE+DyKgeHJeJznQmYMIBG3TKIHJJT95Q58nHLSrElKlGQqDTR2HQ9fx5CN/Gk6Vh/kupBTDLU11/nDk/g==}
    peerDependencies:
      react: ^19.1.0

  react-error-boundary@3.1.4:
    resolution: {integrity: sha512-uM9uPzZJTF6wRQORmSrvOIgt4lJ9MC1sNgEOj2XGsDTRE4kmpWxg7ENK9EWNKJRMAOY9z0MuF4yIfl6gp4sotA==}
    engines: {node: '>=10', npm: '>=6'}
    peerDependencies:
      react: '>=16.13.1'

  react-is@16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==}

  react-is@17.0.2:
    resolution: {integrity: sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w==}

  react-markdown@10.1.0:
    resolution: {integrity: sha512-qKxVopLT/TyA6BX3Ue5NwabOsAzm0Q7kAPwq6L+wWDwisYs7R8vZ0nRXqq6rkueboxpkjvLGU9fWifiX/ZZFxQ==}
    peerDependencies:
      '@types/react': '>=18'
      react: '>=18'

  react-refresh@0.17.0:
    resolution: {integrity: sha512-z6F7K9bV85EfseRCp2bzrpyQ0Gkw1uLoCel9XBVWPg/TjRj94SkJzUTGfOa4bs7iJvBWtQG0Wq7wnI0syw3EBQ==}
    engines: {node: '>=0.10.0'}

  react-syntax-highlighter@15.6.1:
    resolution: {integrity: sha512-OqJ2/vL7lEeV5zTJyG7kmARppUjiB9h9udl4qHQjjgEos66z00Ia0OckwYfRxCSFrW8RJIBnsBwQsHZbVPspqg==}
    peerDependencies:
      react: '>= 0.14.0'

  react-transition-group@4.4.5:
    resolution: {integrity: sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==}
    peerDependencies:
      react: '>=16.6.0'
      react-dom: '>=16.6.0'

  react@19.1.0:
    resolution: {integrity: sha512-FS+XFBNvn3GTAWq26joslQgWNoFu08F4kl0J4CgdNKADkdSGXQyTCnKteIAJy96Br6YbpEU1LSzV5dYtjMkMDg==}
    engines: {node: '>=0.10.0'}

  refractor@3.6.0:
    resolution: {integrity: sha512-MY9W41IOWxxk31o+YvFCNyNzdkc9M20NoZK5vq6jkv4I/uh2zkWcfudj0Q1fovjUQJrNewS9NMzeTtqPf+n5EA==}

  rehype-katex@7.0.1:
    resolution: {integrity: sha512-OiM2wrZ/wuhKkigASodFoo8wimG3H12LWQaH8qSPVJn9apWKFSH3YOCtbKpBorTVw/eI7cuT21XBbvwEswbIOA==}

  rehype-raw@7.0.0:
    resolution: {integrity: sha512-/aE8hCfKlQeA8LmyeyQvQF3eBiLRGNlfBJEvWH7ivp9sBqs7TNqBL5X3v157rM4IFETqDnIOO+z5M/biZbo9Ww==}

  rehype-sanitize@6.0.0:
    resolution: {integrity: sha512-CsnhKNsyI8Tub6L4sm5ZFsme4puGfc6pYylvXo1AeqaGbjOYyzNv3qZPwvs0oMJ39eryyeOdmxwUIo94IpEhqg==}

  rehype-stringify@10.0.1:
    resolution: {integrity: sha512-k9ecfXHmIPuFVI61B9DeLPN0qFHfawM6RsuX48hoqlaKSF61RskNjSm1lI8PhBEM0MRdLxVVm4WmTqJQccH9mA==}

  remark-breaks@4.0.0:
    resolution: {integrity: sha512-IjEjJOkH4FuJvHZVIW0QCDWxcG96kCq7An/KVH2NfJe6rKZU2AsHeB3OEjPNRxi4QC34Xdx7I2KGYn6IpT7gxQ==}

  remark-gfm@4.0.1:
    resolution: {integrity: sha512-1quofZ2RQ9EWdeN34S79+KExV1764+wCUGop5CPL1WGdD0ocPpu91lzPGbwWMECpEpd42kJGQwzRfyov9j4yNg==}

  remark-math@6.0.0:
    resolution: {integrity: sha512-MMqgnP74Igy+S3WwnhQ7kqGlEerTETXMvJhrUzDikVZ2/uogJCb+WHUg97hK9/jcfc0dkD73s3LN8zU49cTEtA==}

  remark-parse@11.0.0:
    resolution: {integrity: sha512-FCxlKLNGknS5ba/1lmpYijMUzX2esxW5xQqjWxw2eHFfS2MSdaHVINFmhjo+qN1WhZhNimq0dZATN9pH0IDrpA==}

  remark-rehype@11.1.2:
    resolution: {integrity: sha512-Dh7l57ianaEoIpzbp0PC9UKAdCSVklD8E5Rpw7ETfbTl3FqcOOgq5q2LVDhgGCkaBv7p24JXikPdvhhmHvKMsw==}

  remark-stringify@11.0.0:
    resolution: {integrity: sha512-1OSmLd3awB/t8qdoEOMazZkNsfVTeY4fTsgzcQFdXNq8ToTN4ZGwrMnlda4K6smTFKD+GRV6O48i6Z4iKgPPpw==}

  remark-supersub@1.0.0:
    resolution: {integrity: sha512-3SYsphMqpAWbr8AZozdcypozinl/lly3e7BEwPG3YT5J9uZQaDcELBF6/sr/OZoAlFxy2nhNFWSrZBu/ZPRT3Q==}

  rollup@4.40.0:
    resolution: {integrity: sha512-Noe455xmA96nnqH5piFtLobsGbCij7Tu+tb3c1vYjNbTkfzGqXqQXG3wJaYXkRZuQ0vEYN4bhwg7QnIrqB5B+w==}
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true

  rtl-css-js@1.16.1:
    resolution: {integrity: sha512-lRQgou1mu19e+Ya0LsTvKrVJ5TYUbqCVPAiImX3UfLTenarvPUl1QFdvu5Z3PYmHT9RCcwIfbjRQBntExyj3Zg==}

  scheduler@0.26.0:
    resolution: {integrity: sha512-NlHwttCI/l5gCPR3D1nNXtWABUmBwvZpEQiD4IXSbIDq8BzLIK/7Ir5gTFSGZDUu37K5cMNp0hFtzO38sC7gWA==}

  semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true

  source-map-js@1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==}
    engines: {node: '>=0.10.0'}

  space-separated-tokens@1.1.5:
    resolution: {integrity: sha512-q/JSVd1Lptzhf5bkYm4ob4iWPjx0KiRe3sRFBNrVqbJkFaBm5vbbowy1mymoPNLRa52+oadOhJ+K49wsSeSjTA==}

  space-separated-tokens@2.0.2:
    resolution: {integrity: sha512-PEGlAwrG8yXGXRjW32fGbg66JAlOAwbObuqVoJpv/mRgoWDQfgH1wDPvtzWyUSNAXBGSk8h755YDbbcEy3SH2Q==}

  stringify-entities@4.0.4:
    resolution: {integrity: sha512-IwfBptatlO+QCJUo19AqvrPNqlVMpW9YEL2LIVY+Rpv2qsjCGxaDLNRgeGsQWJhfItebuJhsGSLjaBbNSQ+ieg==}

  style-to-js@1.1.16:
    resolution: {integrity: sha512-/Q6ld50hKYPH3d/r6nr117TZkHR0w0kGGIVfpG9N6D8NymRPM9RqCUv4pRpJ62E5DqOYx2AFpbZMyCPnjQCnOw==}

  style-to-object@1.0.8:
    resolution: {integrity: sha512-xT47I/Eo0rwJmaXC4oilDGDWLohVhR6o/xAQcPQN8q6QBuZVL8qMYL85kLmST5cPjAorwvqIA4qXTRQoYHaL6g==}

  stylis@4.3.6:
    resolution: {integrity: sha512-yQ3rwFWRfwNUY7H5vpU0wfdkNSnvnJinhF9830Swlaxl03zsOjCfmX0ugac+3LtK0lYSgwL/KXc8oYL3mG4YFQ==}

  tabster@8.5.4:
    resolution: {integrity: sha512-5Fe8vonlp6wjkBuaU3YImZsFncXkdxhCIE6CR28nD0n84kZERIDr9T9wBeya5h1Oj19AhzGFWyZrL6/29tCobA==}

  tinyglobby@0.2.13:
    resolution: {integrity: sha512-mEwzpUgrLySlveBwEVDMKk5B57bhLPYovRfPAXD5gA/98Opn0rCDj3GtLwFvCvH5RK9uPCExUROW5NjDwvqkxw==}
    engines: {node: '>=12.0.0'}

  toggle-selection@1.0.6:
    resolution: {integrity: sha512-BiZS+C1OS8g/q2RRbJmy59xpyghNBqrr6k5L/uKBGRsTfxmu3ffiRnd8mlGPUVayg8pvfi5urfnu8TU7DVOkLQ==}

  trim-lines@3.0.1:
    resolution: {integrity: sha512-kRj8B+YHZCc9kQYdWfJB2/oUl9rA99qbowYYBtr4ui4mZyAQ2JpvVBd/6U2YloATfqBhBTSMhTpgBHtU0Mf3Rg==}

  trough@2.2.0:
    resolution: {integrity: sha512-tmMpK00BjZiUyVyvrBK7knerNgmgvcV/KLVyuma/SC+TQN167GrMRciANTz09+k3zW8L8t60jWO1GpfkZdjTaw==}

  tslib@2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}

  typescript@5.8.3:
    resolution: {integrity: sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==}
    engines: {node: '>=14.17'}
    hasBin: true

  undici-types@6.21.0:
    resolution: {integrity: sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==}

  unified@11.0.5:
    resolution: {integrity: sha512-xKvGhPWw3k84Qjh8bI3ZeJjqnyadK+GEFtazSfZv/rKeTkTjOJho6mFqh2SM96iIcZokxiOpg78GazTSg8+KHA==}

  unist-util-find-after@5.0.0:
    resolution: {integrity: sha512-amQa0Ep2m6hE2g72AugUItjbuM8X8cGQnFoHk0pGfrFeT9GZhzN5SW8nRsiGKK7Aif4CrACPENkA6P/Lw6fHGQ==}

  unist-util-is@5.2.1:
    resolution: {integrity: sha512-u9njyyfEh43npf1M+yGKDGVPbY/JWEemg5nH05ncKPfi+kBbKBJoTdsogMu33uhytuLlv9y0O7GH7fEdwLdLQw==}

  unist-util-is@6.0.0:
    resolution: {integrity: sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==}

  unist-util-position@5.0.0:
    resolution: {integrity: sha512-fucsC7HjXvkB5R3kTCO7kUjRdrS0BJt3M/FPxmHMBOm8JQi2BsHAHFsy27E0EolP8rp0NzXsJ+jNPyDWvOJZPA==}

  unist-util-remove-position@5.0.0:
    resolution: {integrity: sha512-Hp5Kh3wLxv0PHj9m2yZhhLt58KzPtEYKQQ4yxfYFEO7EvHwzyDYnduhHnY1mDxoqr7VUwVuHXk9RXKIiYS1N8Q==}

  unist-util-stringify-position@4.0.0:
    resolution: {integrity: sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==}

  unist-util-visit-parents@5.1.3:
    resolution: {integrity: sha512-x6+y8g7wWMyQhL1iZfhIPhDAs7Xwbn9nRosDXl7qoPTSCy0yNxnKc+hWokFifWQIDGi154rdUqKvbCa4+1kLhg==}

  unist-util-visit-parents@6.0.1:
    resolution: {integrity: sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==}

  unist-util-visit@4.1.2:
    resolution: {integrity: sha512-MSd8OUGISqHdVvfY9TPhyK2VdUrPgxkUtWSuMHF6XAAFuL4LokseigBnZtPnJMu+FbynTkFNnFlyjxpVKujMRg==}

  unist-util-visit@5.0.0:
    resolution: {integrity: sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==}

  update-browserslist-db@1.1.3:
    resolution: {integrity: sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  use-disposable@1.0.4:
    resolution: {integrity: sha512-j83t6AMLWUyb5zwlTDqf6dP9LezM9R0yTbI/b6olmdaGtCKQUe9pgJWV6dRaaQLcozypjIEp4EmZr2DkZGKLSg==}
    peerDependencies:
      '@types/react': '>=16.8.0 <19.0.0'
      '@types/react-dom': '>=16.8.0 <19.0.0'
      react: '>=16.8.0 <19.0.0'
      react-dom: '>=16.8.0 <19.0.0'

  use-sync-external-store@1.5.0:
    resolution: {integrity: sha512-Rb46I4cGGVBmjamjphe8L/UnvJD+uPPtTkNvX5mZgqdbavhI4EbgIWJiIHXJ8bc/i9EQGPRh4DwEURJ552Do0A==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  vfile-location@5.0.3:
    resolution: {integrity: sha512-5yXvWDEgqeiYiBe1lbxYF7UMAIm/IcopxMHrMQDq3nvKcjPKIhZklUKL+AE7J7uApI4kwe2snsK+eI6UTj9EHg==}

  vfile-message@4.0.2:
    resolution: {integrity: sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==}

  vfile@6.0.3:
    resolution: {integrity: sha512-KzIbH/9tXat2u30jf+smMwFCsno4wHVdNmzFyL+T/L3UGqqk6JKfVqOFOZEpZSHADH1k40ab6NUIXZq422ov3Q==}

  vite@6.3.4:
    resolution: {integrity: sha512-BiReIiMS2fyFqbqNT/Qqt4CVITDU9M9vE+DKcVAsB+ZV0wvTKd+3hMbkpxz1b+NmEDMegpVbisKiAZOnvO92Sw==}
    engines: {node: ^18.0.0 || ^20.0.0 || >=22.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^18.0.0 || ^20.0.0 || >=22.0.0
      jiti: '>=1.21.0'
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      sass-embedded: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.16.0
      tsx: ^4.8.1
      yaml: ^2.4.2
    peerDependenciesMeta:
      '@types/node':
        optional: true
      jiti:
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
      tsx:
        optional: true
      yaml:
        optional: true

  web-namespaces@2.0.1:
    resolution: {integrity: sha512-bKr1DkiNa2krS7qxNtdrtHAmzuYGFQLiQ13TsorsdT6ULTkPLKuu5+GsFpDlg6JFjUTwX2DyhMPG2be8uPrqsQ==}

  xtend@4.0.2:
    resolution: {integrity: sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==}
    engines: {node: '>=0.4'}

  yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}

  yjs@13.6.26:
    resolution: {integrity: sha512-wiARO3wixu7mtoRP5f7LqpUtsURP9SmNgXUt3RlnZg4qDuF7dUjthwIvwxIDmK55dPw4Wl4QdW5A3ag0atwu7g==}
    engines: {node: '>=16.0.0', npm: '>=8.0.0'}

  zwitch@2.0.4:
    resolution: {integrity: sha512-bXE4cR/kVZhKZX/RjPEflHaKVhUVl85noU3v6b8apfQEc1x4A+zBxjZ4lN8LqGd6WZ3dl98pY4o717VFmoPp+A==}

snapshots:

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25

  '@babel/code-frame@7.26.2':
    dependencies:
      '@babel/helper-validator-identifier': 7.25.9
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/compat-data@7.26.8': {}

  '@babel/core@7.26.10':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.26.2
      '@babel/generator': 7.27.0
      '@babel/helper-compilation-targets': 7.27.0
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.10)
      '@babel/helpers': 7.27.0
      '@babel/parser': 7.27.0
      '@babel/template': 7.27.0
      '@babel/traverse': 7.27.0
      '@babel/types': 7.27.0
      convert-source-map: 2.0.0
      debug: 4.4.0
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.27.0':
    dependencies:
      '@babel/parser': 7.27.0
      '@babel/types': 7.27.0
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 3.1.0

  '@babel/helper-compilation-targets@7.27.0':
    dependencies:
      '@babel/compat-data': 7.26.8
      '@babel/helper-validator-option': 7.25.9
      browserslist: 4.24.4
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-module-imports@7.25.9':
    dependencies:
      '@babel/traverse': 7.27.0
      '@babel/types': 7.27.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-transforms@7.26.0(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-validator-identifier': 7.25.9
      '@babel/traverse': 7.27.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-plugin-utils@7.26.5': {}

  '@babel/helper-string-parser@7.25.9': {}

  '@babel/helper-validator-identifier@7.25.9': {}

  '@babel/helper-validator-option@7.25.9': {}

  '@babel/helpers@7.27.0':
    dependencies:
      '@babel/template': 7.27.0
      '@babel/types': 7.27.0

  '@babel/parser@7.27.0':
    dependencies:
      '@babel/types': 7.27.0

  '@babel/plugin-transform-react-jsx-self@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-react-jsx-source@7.25.9(@babel/core@7.26.10)':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/runtime@7.27.1': {}

  '@babel/template@7.27.0':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/parser': 7.27.0
      '@babel/types': 7.27.0

  '@babel/traverse@7.27.0':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/generator': 7.27.0
      '@babel/parser': 7.27.0
      '@babel/template': 7.27.0
      '@babel/types': 7.27.0
      debug: 4.4.0
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.27.0':
    dependencies:
      '@babel/helper-string-parser': 7.25.9
      '@babel/helper-validator-identifier': 7.25.9

  '@emotion/hash@0.9.2': {}

  '@esbuild/aix-ppc64@0.25.3':
    optional: true

  '@esbuild/android-arm64@0.25.3':
    optional: true

  '@esbuild/android-arm@0.25.3':
    optional: true

  '@esbuild/android-x64@0.25.3':
    optional: true

  '@esbuild/darwin-arm64@0.25.3':
    optional: true

  '@esbuild/darwin-x64@0.25.3':
    optional: true

  '@esbuild/freebsd-arm64@0.25.3':
    optional: true

  '@esbuild/freebsd-x64@0.25.3':
    optional: true

  '@esbuild/linux-arm64@0.25.3':
    optional: true

  '@esbuild/linux-arm@0.25.3':
    optional: true

  '@esbuild/linux-ia32@0.25.3':
    optional: true

  '@esbuild/linux-loong64@0.25.3':
    optional: true

  '@esbuild/linux-mips64el@0.25.3':
    optional: true

  '@esbuild/linux-ppc64@0.25.3':
    optional: true

  '@esbuild/linux-riscv64@0.25.3':
    optional: true

  '@esbuild/linux-s390x@0.25.3':
    optional: true

  '@esbuild/linux-x64@0.25.3':
    optional: true

  '@esbuild/netbsd-arm64@0.25.3':
    optional: true

  '@esbuild/netbsd-x64@0.25.3':
    optional: true

  '@esbuild/openbsd-arm64@0.25.3':
    optional: true

  '@esbuild/openbsd-x64@0.25.3':
    optional: true

  '@esbuild/sunos-x64@0.25.3':
    optional: true

  '@esbuild/win32-arm64@0.25.3':
    optional: true

  '@esbuild/win32-ia32@0.25.3':
    optional: true

  '@esbuild/win32-x64@0.25.3':
    optional: true

  '@floating-ui/core@1.6.9':
    dependencies:
      '@floating-ui/utils': 0.2.9

  '@floating-ui/devtools@0.2.1(@floating-ui/dom@1.6.13)':
    dependencies:
      '@floating-ui/dom': 1.6.13

  '@floating-ui/dom@1.6.13':
    dependencies:
      '@floating-ui/core': 1.6.9
      '@floating-ui/utils': 0.2.9

  '@floating-ui/utils@0.2.9': {}

  '@fluentui-contrib/houdini-utils@0.3.4(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui-copilot/chat-input-plugins@0.4.2(@lexical/clipboard@0.12.6(lexical@0.12.6))':
    dependencies:
      '@fluentui-copilot/text-editor': 0.3.1(@lexical/clipboard@0.12.6(lexical@0.12.6))
      '@swc/helpers': 0.5.17
    transitivePeerDependencies:
      - '@lexical/clipboard'

  '@fluentui-copilot/flair@0.4.10(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui-contrib/houdini-utils': 0.3.4(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/tokens': 0.3.10
      '@swc/helpers': 0.5.17
    transitivePeerDependencies:
      - '@types/react'
      - '@types/react-dom'
      - react
      - react-dom

  '@fluentui-copilot/react-announce@0.5.7(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui-copilot/react-attachments@0.10.7(@fluentui/keyboard-keys@9.0.8)(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui-copilot/react-provider': 0.9.4(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-utilities': 0.0.5(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/tokens': 0.3.10
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui-copilot/react-chat-input-plugins@0.4.8(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@lexical/clipboard@0.12.6(lexical@0.12.6))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)':
    dependencies:
      '@fluentui-copilot/chat-input-plugins': 0.4.2(@lexical/clipboard@0.12.6(lexical@0.12.6))
      '@fluentui-copilot/react-text-editor': 0.4.1(@lexical/clipboard@0.12.6(lexical@0.12.6))(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)
      '@fluentui-copilot/text-editor': 0.3.1(@lexical/clipboard@0.12.6(lexical@0.12.6))
      '@fluentui-copilot/tokens': 0.3.10
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - '@lexical/clipboard'
      - lexical
      - yjs

  '@fluentui-copilot/react-chat-input@0.11.3(@fluentui/keyboard-keys@9.0.8)(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-motion@9.7.2(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@lexical/clipboard@0.12.6(lexical@0.12.6))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)':
    dependencies:
      '@fluentui-copilot/chat-input-plugins': 0.4.2(@lexical/clipboard@0.12.6(lexical@0.12.6))
      '@fluentui-copilot/react-chat-input-plugins': 0.4.8(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@lexical/clipboard@0.12.6(lexical@0.12.6))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)
      '@fluentui-copilot/react-editor-input': 0.4.9(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@lexical/clipboard@0.12.6(lexical@0.12.6))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)
      '@fluentui-copilot/react-provider': 0.9.4(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-text-editor': 0.4.1(@lexical/clipboard@0.12.6(lexical@0.12.6))(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)
      '@fluentui-copilot/react-utilities': 0.0.5(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/text-editor': 0.3.1(@lexical/clipboard@0.12.6(lexical@0.12.6))
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-motion': 9.7.2(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - '@lexical/clipboard'
      - lexical
      - yjs

  '@fluentui-copilot/react-copilot-chat@0.9.6(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-tabster@9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui-copilot/react-output-card': 0.9.5(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-provider': 0.9.4(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/tokens': 0.3.10
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-positioning': 9.16.7(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui-copilot/react-copilot@0.23.3(2cabaf760f2a31c97b9cc994068d61b2)':
    dependencies:
      '@fluentui-copilot/chat-input-plugins': 0.4.2(@lexical/clipboard@0.12.6(lexical@0.12.6))
      '@fluentui-copilot/react-attachments': 0.10.7(@fluentui/keyboard-keys@9.0.8)(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-chat-input': 0.11.3(@fluentui/keyboard-keys@9.0.8)(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-motion@9.7.2(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@lexical/clipboard@0.12.6(lexical@0.12.6))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)
      '@fluentui-copilot/react-chat-input-plugins': 0.4.8(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@lexical/clipboard@0.12.6(lexical@0.12.6))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)
      '@fluentui-copilot/react-copilot-chat': 0.9.6(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-tabster@9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-editor-input': 0.4.9(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@lexical/clipboard@0.12.6(lexical@0.12.6))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)
      '@fluentui-copilot/react-entity-cards': 0.1.4(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-feedback-buttons': 0.9.6(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-first-run-experience': 0.6.4(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-flair': 0.5.13(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-latency': 0.8.5(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-output-card': 0.9.5(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-preview': 0.5.8(@fluentui/keyboard-keys@9.0.8)(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-prompt-input': 0.5.4(@fluentui/keyboard-keys@9.0.8)(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-tabster@9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@lexical/clipboard@0.12.6(lexical@0.12.6))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)
      '@fluentui-copilot/react-prompt-listbox': 0.5.2(********************************)
      '@fluentui-copilot/react-prompt-starter': 0.7.5(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-provider': 0.9.4(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-reference': 0.13.10(@fluentui/keyboard-keys@9.0.8)(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-response-count': 0.2.13(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-sensitivity-label': 0.5.8(@fluentui/keyboard-keys@9.0.8)(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-snippet': 0.2.14(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-suggestions': 0.10.1(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-text-editor': 0.4.1(@lexical/clipboard@0.12.6(lexical@0.12.6))(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)
      '@fluentui-copilot/react-textarea': 0.8.5(@fluentui/keyboard-keys@9.0.8)(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/text-editor': 0.3.1(@lexical/clipboard@0.12.6(lexical@0.12.6))
      '@fluentui-copilot/tokens': 0.3.10
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.14.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-motion': 9.7.2(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - '@fluentui/react-combobox'
      - '@fluentui/react-positioning'
      - '@lexical/clipboard'
      - lexical
      - yjs

  '@fluentui-copilot/react-editor-input@0.4.9(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@lexical/clipboard@0.12.6(lexical@0.12.6))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)':
    dependencies:
      '@fluentui-copilot/chat-input-plugins': 0.4.2(@lexical/clipboard@0.12.6(lexical@0.12.6))
      '@fluentui-copilot/react-chat-input-plugins': 0.4.8(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@lexical/clipboard@0.12.6(lexical@0.12.6))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)
      '@fluentui-copilot/react-text-editor': 0.4.1(@lexical/clipboard@0.12.6(lexical@0.12.6))(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)
      '@fluentui-copilot/tokens': 0.3.10
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - '@lexical/clipboard'
      - lexical
      - yjs

  '@fluentui-copilot/react-entity-cards@0.1.4(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui-copilot/react-provider': 0.9.4(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - '@fluentui/react-shared-contexts'

  '@fluentui-copilot/react-feedback-buttons@0.9.6(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui-copilot/react-provider': 0.9.4(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui-copilot/react-first-run-experience@0.6.4(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui-copilot/react-flair@0.5.13(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui-contrib/houdini-utils': 0.3.4(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/flair': 0.4.10(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/tokens': 0.3.10
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui-copilot/react-input-listbox@0.1.3(********************************)':
    dependencies:
      '@fluentui-copilot/react-provider': 0.9.4(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-utilities': 0.0.5(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.14.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-positioning': 9.16.7(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui-copilot/react-latency@0.8.5(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui-copilot/react-provider': 0.9.4(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui-copilot/react-output-card@0.9.5(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui-copilot/react-flair': 0.5.13(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-provider': 0.9.4(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/tokens': 0.3.10
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui-copilot/react-preview@0.5.8(@fluentui/keyboard-keys@9.0.8)(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui-copilot/react-prompt-input@0.5.4(@fluentui/keyboard-keys@9.0.8)(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-tabster@9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@lexical/clipboard@0.12.6(lexical@0.12.6))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)':
    dependencies:
      '@fluentui-copilot/chat-input-plugins': 0.4.2(@lexical/clipboard@0.12.6(lexical@0.12.6))
      '@fluentui-copilot/react-chat-input-plugins': 0.4.8(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@lexical/clipboard@0.12.6(lexical@0.12.6))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)
      '@fluentui-copilot/react-editor-input': 0.4.9(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@lexical/clipboard@0.12.6(lexical@0.12.6))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)
      '@fluentui-copilot/react-provider': 0.9.4(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-text-editor': 0.4.1(@lexical/clipboard@0.12.6(lexical@0.12.6))(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)
      '@fluentui-copilot/react-utilities': 0.0.5(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/text-editor': 0.3.1(@lexical/clipboard@0.12.6(lexical@0.12.6))
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - '@fluentui/react-context-selector'
      - '@lexical/clipboard'
      - lexical
      - yjs

  '@fluentui-copilot/react-prompt-listbox@0.5.2(********************************)':
    dependencies:
      '@fluentui-copilot/chat-input-plugins': 0.4.2(@lexical/clipboard@0.12.6(lexical@0.12.6))
      '@fluentui-copilot/react-chat-input-plugins': 0.4.8(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@lexical/clipboard@0.12.6(lexical@0.12.6))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)
      '@fluentui-copilot/react-editor-input': 0.4.9(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@lexical/clipboard@0.12.6(lexical@0.12.6))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)
      '@fluentui-copilot/react-input-listbox': 0.1.3(********************************)
      '@fluentui-copilot/react-prompt-input': 0.5.4(@fluentui/keyboard-keys@9.0.8)(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-tabster@9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@lexical/clipboard@0.12.6(lexical@0.12.6))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)
      '@fluentui-copilot/react-provider': 0.9.4(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-text-editor': 0.4.1(@lexical/clipboard@0.12.6(lexical@0.12.6))(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)
      '@fluentui-copilot/text-editor': 0.3.1(@lexical/clipboard@0.12.6(lexical@0.12.6))
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.14.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-combobox': 9.14.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-positioning': 9.16.7(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - '@fluentui/react-context-selector'
      - '@lexical/clipboard'
      - lexical
      - yjs

  '@fluentui-copilot/react-prompt-starter@0.7.5(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui-copilot/react-provider': 0.9.4(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui-copilot/react-provider@0.9.4(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui-copilot/react-announce': 0.5.7(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/tokens': 0.3.10
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui-copilot/react-reference@0.13.10(@fluentui/keyboard-keys@9.0.8)(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui-copilot/react-preview': 0.5.8(@fluentui/keyboard-keys@9.0.8)(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-provider': 0.9.4(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-sensitivity-label': 0.5.8(@fluentui/keyboard-keys@9.0.8)(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui-copilot/react-utilities': 0.0.5(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui-copilot/react-response-count@0.2.13(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui-copilot/tokens': 0.3.10
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui-copilot/react-sensitivity-label@0.5.8(@fluentui/keyboard-keys@9.0.8)(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui-copilot/react-preview': 0.5.8(@fluentui/keyboard-keys@9.0.8)(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui-copilot/react-snippet@0.2.14(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui-copilot/tokens': 0.3.10
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui-copilot/react-suggestions@0.10.1(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui-copilot/react-provider': 0.9.4(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui-copilot/react-text-editor@0.4.1(@lexical/clipboard@0.12.6(lexical@0.12.6))(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)':
    dependencies:
      '@fluentui-copilot/text-editor': 0.3.1(@lexical/clipboard@0.12.6(lexical@0.12.6))
      '@lexical/react': 0.12.6(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)
      '@swc/helpers': 0.5.17
    transitivePeerDependencies:
      - '@lexical/clipboard'
      - lexical
      - react
      - react-dom
      - yjs

  '@fluentui-copilot/react-textarea@0.8.5(@fluentui/keyboard-keys@9.0.8)(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-icons@2.0.279(react@19.1.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui-copilot/react-provider': 0.9.4(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui-copilot/react-utilities@0.0.5(@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0))(@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0))(@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0))(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/react-components': 9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui-copilot/text-editor@0.3.1(@lexical/clipboard@0.12.6(lexical@0.12.6))':
    dependencies:
      '@lexical/headless': 0.12.6(lexical@0.12.6)
      '@lexical/list': 0.12.6(lexical@0.12.6)
      '@lexical/plain-text': 0.12.6(@lexical/clipboard@0.12.6(lexical@0.12.6))(@lexical/selection@0.12.6(lexical@0.12.6))(@lexical/utils@0.12.6(lexical@0.12.6))(lexical@0.12.6)
      '@lexical/rich-text': 0.12.6(@lexical/clipboard@0.12.6(lexical@0.12.6))(@lexical/selection@0.12.6(lexical@0.12.6))(@lexical/utils@0.12.6(lexical@0.12.6))(lexical@0.12.6)
      '@lexical/selection': 0.12.6(lexical@0.12.6)
      '@lexical/utils': 0.12.6(lexical@0.12.6)
      '@swc/helpers': 0.5.17
      lexical: 0.12.6
    transitivePeerDependencies:
      - '@lexical/clipboard'

  '@fluentui-copilot/tokens@0.3.10':
    dependencies:
      '@fluentui/react-theme': 9.1.24
      '@fluentui/tokens': 1.0.0-alpha.21
      '@swc/helpers': 0.5.17

  '@fluentui/keyboard-keys@9.0.8':
    dependencies:
      '@swc/helpers': 0.5.17

  '@fluentui/priority-overflow@9.1.15':
    dependencies:
      '@swc/helpers': 0.5.17

  '@fluentui/react-accordion@9.6.8(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-aria': 9.14.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-motion': 9.7.2(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-motion-components-preview': 0.4.9(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-alert@9.0.0-beta.124(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-avatar': 9.7.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-button': 9.4.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-aria@9.14.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui/react-avatar@9.7.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-badge': 9.2.54(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-popover': 9.10.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-tooltip': 9.6.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-badge@9.2.54(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui/react-breadcrumb@9.1.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/react-aria': 9.14.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-button': 9.4.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-link': 9.4.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui/react-button@9.4.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.14.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui/react-card@9.2.5(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-text': 9.4.36(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui/react-carousel@9.6.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-aria': 9.14.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-button': 9.4.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-tooltip': 9.6.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      embla-carousel: 8.6.0
      embla-carousel-autoplay: 8.6.0(embla-carousel@8.6.0)
      embla-carousel-fade: 8.6.0(embla-carousel@8.6.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-checkbox@9.3.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-field': 9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-label': 9.1.87(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-combobox@9.14.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.14.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-field': 9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-portal': 9.5.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-positioning': 9.16.7(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-components@9.60.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-accordion': 9.6.8(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-alert': 9.0.0-beta.124(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-aria': 9.14.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-avatar': 9.7.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-badge': 9.2.54(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-breadcrumb': 9.1.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-button': 9.4.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-card': 9.2.5(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-carousel': 9.6.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-checkbox': 9.3.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-combobox': 9.14.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-dialog': 9.12.8(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-divider': 9.2.86(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-drawer': 9.7.8(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-field': 9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-image': 9.1.84(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-infobutton': 9.0.0-beta.102(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-infolabel': 9.2.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-input': 9.5.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-label': 9.1.87(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-link': 9.4.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-list': 9.1.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-menu': 9.16.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-message-bar': 9.4.7(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-motion': 9.7.2(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-overflow': 9.3.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-persona': 9.3.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-popover': 9.10.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-portal': 9.5.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-positioning': 9.16.7(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-progress': 9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-provider': 9.20.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-radio': 9.3.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-rating': 9.1.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-search': 9.1.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-select': 9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-skeleton': 9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-slider': 9.3.7(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-spinbutton': 9.3.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-spinner': 9.5.11(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-swatch-picker': 9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-switch': 9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-table': 9.16.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-tabs': 9.7.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-tag-picker': 9.5.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-tags': 9.5.4(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-teaching-popover': 9.4.5(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-text': 9.4.36(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-textarea': 9.4.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-toast': 9.4.8(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-toolbar': 9.4.5(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-tooltip': 9.6.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-tree': 9.10.9(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-virtualizer': 9.0.0-alpha.92(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-context-selector@9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      scheduler: 0.26.0

  '@fluentui/react-dialog@9.12.8(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.14.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-motion': 9.7.2(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-motion-components-preview': 0.4.9(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-portal': 9.5.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-divider@9.2.86(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui/react-drawer@9.7.8(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-dialog': 9.12.8(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-motion': 9.7.2(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-portal': 9.5.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-field@9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-label': 9.1.87(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-icons@2.0.279(react@19.1.0)':
    dependencies:
      '@griffel/react': 1.5.30(react@19.1.0)
      react: 19.1.0
      tslib: 2.8.1

  '@fluentui/react-image@9.1.84(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui/react-infobutton@9.0.0-beta.102(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-label': 9.1.87(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-popover': 9.10.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-infolabel@9.2.0(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-label': 9.1.87(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-popover': 9.10.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-input@9.5.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-field': 9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-jsx-runtime@9.0.54(@types/react@18.3.20)(react@19.1.0)':
    dependencies:
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      react: 19.1.0
      react-is: 17.0.2

  '@fluentui/react-label@9.1.87(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui/react-link@9.4.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui/react-list@9.1.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-checkbox': 9.3.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-menu@9.16.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.14.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-portal': 9.5.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-positioning': 9.16.7(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-message-bar@9.4.7(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/react-button': 9.4.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-link': 9.4.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      react-transition-group: 4.4.5(react-dom@19.1.0(react@19.1.0))(react@19.1.0)

  '@fluentui/react-motion-components-preview@0.4.9(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/react-motion': 9.7.2(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui/react-motion@9.7.2(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui/react-overflow@9.3.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/priority-overflow': 9.1.15
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-persona@9.3.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-avatar': 9.7.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-badge': 9.2.54(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-popover@9.10.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.14.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-portal': 9.5.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-positioning': 9.16.7(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-portal@9.5.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      use-disposable: 1.0.4(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)

  '@fluentui/react-positioning@9.16.7(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@floating-ui/devtools': 0.2.1(@floating-ui/dom@1.6.13)
      '@floating-ui/dom': 1.6.13
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui/react-progress@9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-field': 9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-provider@9.20.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/core': 1.19.2
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui/react-radio@9.3.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-field': 9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-label': 9.1.87(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-rating@9.1.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui/react-search@9.1.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-input': 9.5.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-select@9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-field': 9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-shared-contexts@9.23.1(@types/react@18.3.20)(react@19.1.0)':
    dependencies:
      '@fluentui/react-theme': 9.1.24
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      react: 19.1.0

  '@fluentui/react-skeleton@9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-field': 9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-slider@9.3.7(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-field': 9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-spinbutton@9.3.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-field': 9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-spinner@9.5.11(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-label': 9.1.87(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui/react-swatch-picker@9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-field': 9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-switch@9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-field': 9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-label': 9.1.87(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-table@9.16.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.14.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-avatar': 9.7.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-checkbox': 9.3.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-radio': 9.3.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-tabs@9.7.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-tabster@9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      keyborg: 2.6.0
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      tabster: 8.5.4

  '@fluentui/react-tag-picker@9.5.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.14.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-combobox': 9.14.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-field': 9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-portal': 9.5.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-positioning': 9.16.7(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-tags': 9.5.4(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-tags@9.5.4(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.14.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-avatar': 9.7.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-teaching-popover@9.4.5(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-aria': 9.14.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-button': 9.4.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-popover': 9.10.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      use-sync-external-store: 1.5.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-text@9.4.36(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui/react-textarea@9.4.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-field': 9.2.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-theme@9.1.24':
    dependencies:
      '@fluentui/tokens': 1.0.0-alpha.21
      '@swc/helpers': 0.5.17

  '@fluentui/react-toast@9.4.8(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.14.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-motion': 9.7.2(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-motion-components-preview': 0.4.9(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-portal': 9.5.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui/react-toolbar@9.4.5(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/react-button': 9.4.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-divider': 9.2.86(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-radio': 9.3.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-tooltip@9.6.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-portal': 9.5.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-positioning': 9.16.7(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui/react-tree@9.10.9(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-aria': 9.14.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-avatar': 9.7.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-button': 9.4.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-checkbox': 9.3.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-context-selector': 9.1.76(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-icons': 2.0.279(react@19.1.0)
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-motion': 9.7.2(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-motion-components-preview': 0.4.9(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-radio': 9.3.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(scheduler@0.26.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-tabster': 9.24.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@fluentui/react-theme': 9.1.24
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - scheduler

  '@fluentui/react-utilities@9.19.0(@types/react@18.3.20)(react@19.1.0)':
    dependencies:
      '@fluentui/keyboard-keys': 9.0.8
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      react: 19.1.0

  '@fluentui/react-virtualizer@9.0.0-alpha.92(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@fluentui/react-jsx-runtime': 9.0.54(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-shared-contexts': 9.23.1(@types/react@18.3.20)(react@19.1.0)
      '@fluentui/react-utilities': 9.19.0(@types/react@18.3.20)(react@19.1.0)
      '@griffel/react': 1.5.30(react@19.1.0)
      '@swc/helpers': 0.5.17
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@fluentui/tokens@1.0.0-alpha.21':
    dependencies:
      '@swc/helpers': 0.5.17

  '@griffel/core@1.19.2':
    dependencies:
      '@emotion/hash': 0.9.2
      '@griffel/style-types': 1.3.0
      csstype: 3.1.3
      rtl-css-js: 1.16.1
      stylis: 4.3.6
      tslib: 2.8.1

  '@griffel/react@1.5.30(react@19.1.0)':
    dependencies:
      '@griffel/core': 1.19.2
      react: 19.1.0
      tslib: 2.8.1

  '@griffel/style-types@1.3.0':
    dependencies:
      csstype: 3.1.3

  '@jridgewell/gen-mapping@0.3.8':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/sourcemap-codec@1.5.0': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  '@lexical/clipboard@0.12.6(lexical@0.12.6)':
    dependencies:
      '@lexical/html': 0.12.6(lexical@0.12.6)
      '@lexical/list': 0.12.6(lexical@0.12.6)
      '@lexical/selection': 0.12.6(lexical@0.12.6)
      '@lexical/utils': 0.12.6(lexical@0.12.6)
      lexical: 0.12.6

  '@lexical/code@0.12.6(lexical@0.12.6)':
    dependencies:
      '@lexical/utils': 0.12.6(lexical@0.12.6)
      lexical: 0.12.6
      prismjs: 1.30.0

  '@lexical/dragon@0.12.6(lexical@0.12.6)':
    dependencies:
      lexical: 0.12.6

  '@lexical/hashtag@0.12.6(lexical@0.12.6)':
    dependencies:
      '@lexical/utils': 0.12.6(lexical@0.12.6)
      lexical: 0.12.6

  '@lexical/headless@0.12.6(lexical@0.12.6)':
    dependencies:
      lexical: 0.12.6

  '@lexical/history@0.12.6(lexical@0.12.6)':
    dependencies:
      '@lexical/utils': 0.12.6(lexical@0.12.6)
      lexical: 0.12.6

  '@lexical/html@0.12.6(lexical@0.12.6)':
    dependencies:
      '@lexical/selection': 0.12.6(lexical@0.12.6)
      '@lexical/utils': 0.12.6(lexical@0.12.6)
      lexical: 0.12.6

  '@lexical/link@0.12.6(lexical@0.12.6)':
    dependencies:
      '@lexical/utils': 0.12.6(lexical@0.12.6)
      lexical: 0.12.6

  '@lexical/list@0.12.6(lexical@0.12.6)':
    dependencies:
      '@lexical/utils': 0.12.6(lexical@0.12.6)
      lexical: 0.12.6

  '@lexical/mark@0.12.6(lexical@0.12.6)':
    dependencies:
      '@lexical/utils': 0.12.6(lexical@0.12.6)
      lexical: 0.12.6

  '@lexical/markdown@0.12.6(@lexical/clipboard@0.12.6(lexical@0.12.6))(@lexical/selection@0.12.6(lexical@0.12.6))(lexical@0.12.6)':
    dependencies:
      '@lexical/code': 0.12.6(lexical@0.12.6)
      '@lexical/link': 0.12.6(lexical@0.12.6)
      '@lexical/list': 0.12.6(lexical@0.12.6)
      '@lexical/rich-text': 0.12.6(@lexical/clipboard@0.12.6(lexical@0.12.6))(@lexical/selection@0.12.6(lexical@0.12.6))(@lexical/utils@0.12.6(lexical@0.12.6))(lexical@0.12.6)
      '@lexical/text': 0.12.6(lexical@0.12.6)
      '@lexical/utils': 0.12.6(lexical@0.12.6)
      lexical: 0.12.6
    transitivePeerDependencies:
      - '@lexical/clipboard'
      - '@lexical/selection'

  '@lexical/offset@0.12.6(lexical@0.12.6)':
    dependencies:
      lexical: 0.12.6

  '@lexical/overflow@0.12.6(lexical@0.12.6)':
    dependencies:
      lexical: 0.12.6

  '@lexical/plain-text@0.12.6(@lexical/clipboard@0.12.6(lexical@0.12.6))(@lexical/selection@0.12.6(lexical@0.12.6))(@lexical/utils@0.12.6(lexical@0.12.6))(lexical@0.12.6)':
    dependencies:
      '@lexical/clipboard': 0.12.6(lexical@0.12.6)
      '@lexical/selection': 0.12.6(lexical@0.12.6)
      '@lexical/utils': 0.12.6(lexical@0.12.6)
      lexical: 0.12.6

  '@lexical/react@0.12.6(lexical@0.12.6)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(yjs@13.6.26)':
    dependencies:
      '@lexical/clipboard': 0.12.6(lexical@0.12.6)
      '@lexical/code': 0.12.6(lexical@0.12.6)
      '@lexical/dragon': 0.12.6(lexical@0.12.6)
      '@lexical/hashtag': 0.12.6(lexical@0.12.6)
      '@lexical/history': 0.12.6(lexical@0.12.6)
      '@lexical/link': 0.12.6(lexical@0.12.6)
      '@lexical/list': 0.12.6(lexical@0.12.6)
      '@lexical/mark': 0.12.6(lexical@0.12.6)
      '@lexical/markdown': 0.12.6(@lexical/clipboard@0.12.6(lexical@0.12.6))(@lexical/selection@0.12.6(lexical@0.12.6))(lexical@0.12.6)
      '@lexical/overflow': 0.12.6(lexical@0.12.6)
      '@lexical/plain-text': 0.12.6(@lexical/clipboard@0.12.6(lexical@0.12.6))(@lexical/selection@0.12.6(lexical@0.12.6))(@lexical/utils@0.12.6(lexical@0.12.6))(lexical@0.12.6)
      '@lexical/rich-text': 0.12.6(@lexical/clipboard@0.12.6(lexical@0.12.6))(@lexical/selection@0.12.6(lexical@0.12.6))(@lexical/utils@0.12.6(lexical@0.12.6))(lexical@0.12.6)
      '@lexical/selection': 0.12.6(lexical@0.12.6)
      '@lexical/table': 0.12.6(lexical@0.12.6)
      '@lexical/text': 0.12.6(lexical@0.12.6)
      '@lexical/utils': 0.12.6(lexical@0.12.6)
      '@lexical/yjs': 0.12.6(lexical@0.12.6)(yjs@13.6.26)
      lexical: 0.12.6
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      react-error-boundary: 3.1.4(react@19.1.0)
    transitivePeerDependencies:
      - yjs

  '@lexical/rich-text@0.12.6(@lexical/clipboard@0.12.6(lexical@0.12.6))(@lexical/selection@0.12.6(lexical@0.12.6))(@lexical/utils@0.12.6(lexical@0.12.6))(lexical@0.12.6)':
    dependencies:
      '@lexical/clipboard': 0.12.6(lexical@0.12.6)
      '@lexical/selection': 0.12.6(lexical@0.12.6)
      '@lexical/utils': 0.12.6(lexical@0.12.6)
      lexical: 0.12.6

  '@lexical/selection@0.12.6(lexical@0.12.6)':
    dependencies:
      lexical: 0.12.6

  '@lexical/table@0.12.6(lexical@0.12.6)':
    dependencies:
      '@lexical/utils': 0.12.6(lexical@0.12.6)
      lexical: 0.12.6

  '@lexical/text@0.12.6(lexical@0.12.6)':
    dependencies:
      lexical: 0.12.6

  '@lexical/utils@0.12.6(lexical@0.12.6)':
    dependencies:
      '@lexical/list': 0.12.6(lexical@0.12.6)
      '@lexical/selection': 0.12.6(lexical@0.12.6)
      '@lexical/table': 0.12.6(lexical@0.12.6)
      lexical: 0.12.6

  '@lexical/yjs@0.12.6(lexical@0.12.6)(yjs@13.6.26)':
    dependencies:
      '@lexical/offset': 0.12.6(lexical@0.12.6)
      lexical: 0.12.6
      yjs: 13.6.26

  '@rollup/rollup-android-arm-eabi@4.40.0':
    optional: true

  '@rollup/rollup-android-arm64@4.40.0':
    optional: true

  '@rollup/rollup-darwin-arm64@4.40.0':
    optional: true

  '@rollup/rollup-darwin-x64@4.40.0':
    optional: true

  '@rollup/rollup-freebsd-arm64@4.40.0':
    optional: true

  '@rollup/rollup-freebsd-x64@4.40.0':
    optional: true

  '@rollup/rollup-linux-arm-gnueabihf@4.40.0':
    optional: true

  '@rollup/rollup-linux-arm-musleabihf@4.40.0':
    optional: true

  '@rollup/rollup-linux-arm64-gnu@4.40.0':
    optional: true

  '@rollup/rollup-linux-arm64-musl@4.40.0':
    optional: true

  '@rollup/rollup-linux-loongarch64-gnu@4.40.0':
    optional: true

  '@rollup/rollup-linux-powerpc64le-gnu@4.40.0':
    optional: true

  '@rollup/rollup-linux-riscv64-gnu@4.40.0':
    optional: true

  '@rollup/rollup-linux-riscv64-musl@4.40.0':
    optional: true

  '@rollup/rollup-linux-s390x-gnu@4.40.0':
    optional: true

  '@rollup/rollup-linux-x64-gnu@4.40.0':
    optional: true

  '@rollup/rollup-linux-x64-musl@4.40.0':
    optional: true

  '@rollup/rollup-win32-arm64-msvc@4.40.0':
    optional: true

  '@rollup/rollup-win32-ia32-msvc@4.40.0':
    optional: true

  '@rollup/rollup-win32-x64-msvc@4.40.0':
    optional: true

  '@swc/helpers@0.5.17':
    dependencies:
      tslib: 2.8.1

  '@types/babel__core@7.20.5':
    dependencies:
      '@babel/parser': 7.27.0
      '@babel/types': 7.27.0
      '@types/babel__generator': 7.27.0
      '@types/babel__template': 7.4.4
      '@types/babel__traverse': 7.20.7

  '@types/babel__generator@7.27.0':
    dependencies:
      '@babel/types': 7.27.0

  '@types/babel__template@7.4.4':
    dependencies:
      '@babel/parser': 7.27.0
      '@babel/types': 7.27.0

  '@types/babel__traverse@7.20.7':
    dependencies:
      '@babel/types': 7.27.0

  '@types/debug@4.1.12':
    dependencies:
      '@types/ms': 2.1.0

  '@types/estree-jsx@1.0.5':
    dependencies:
      '@types/estree': 1.0.7

  '@types/estree@1.0.7': {}

  '@types/hast@2.3.10':
    dependencies:
      '@types/unist': 2.0.11

  '@types/hast@3.0.4':
    dependencies:
      '@types/unist': 3.0.3

  '@types/katex@0.16.7': {}

  '@types/mdast@4.0.4':
    dependencies:
      '@types/unist': 3.0.3

  '@types/ms@2.1.0': {}

  '@types/node@22.14.1':
    dependencies:
      undici-types: 6.21.0

  '@types/prop-types@15.7.14': {}

  '@types/react-dom@18.3.5(@types/react@18.3.20)':
    dependencies:
      '@types/react': 18.3.20

  '@types/react-syntax-highlighter@15.5.13':
    dependencies:
      '@types/react': 18.3.20

  '@types/react@18.3.20':
    dependencies:
      '@types/prop-types': 15.7.14
      csstype: 3.1.3

  '@types/unist@2.0.11': {}

  '@types/unist@3.0.3': {}

  '@ungap/structured-clone@1.3.0': {}

  '@vitejs/plugin-react@4.4.1(vite@6.3.4(@types/node@22.14.1))':
    dependencies:
      '@babel/core': 7.26.10
      '@babel/plugin-transform-react-jsx-self': 7.25.9(@babel/core@7.26.10)
      '@babel/plugin-transform-react-jsx-source': 7.25.9(@babel/core@7.26.10)
      '@types/babel__core': 7.20.5
      react-refresh: 0.17.0
      vite: 6.3.4(@types/node@22.14.1)
    transitivePeerDependencies:
      - supports-color

  bail@2.0.2: {}

  browserslist@4.24.4:
    dependencies:
      caniuse-lite: 1.0.30001715
      electron-to-chromium: 1.5.142
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3(browserslist@4.24.4)

  caniuse-lite@1.0.30001715: {}

  ccount@2.0.1: {}

  character-entities-html4@2.1.0: {}

  character-entities-legacy@1.1.4: {}

  character-entities-legacy@3.0.0: {}

  character-entities@1.2.4: {}

  character-entities@2.0.2: {}

  character-reference-invalid@1.1.4: {}

  character-reference-invalid@2.0.1: {}

  clsx@2.1.1: {}

  comma-separated-tokens@1.0.8: {}

  comma-separated-tokens@2.0.3: {}

  commander@8.3.0: {}

  convert-source-map@2.0.0: {}

  copy-to-clipboard@3.3.3:
    dependencies:
      toggle-selection: 1.0.6

  csstype@3.1.3: {}

  debug@4.4.0:
    dependencies:
      ms: 2.1.3

  decode-named-character-reference@1.1.0:
    dependencies:
      character-entities: 2.0.2

  dequal@2.0.3: {}

  devlop@1.1.0:
    dependencies:
      dequal: 2.0.3

  dom-helpers@5.2.1:
    dependencies:
      '@babel/runtime': 7.27.1
      csstype: 3.1.3

  electron-to-chromium@1.5.142: {}

  embla-carousel-autoplay@8.6.0(embla-carousel@8.6.0):
    dependencies:
      embla-carousel: 8.6.0

  embla-carousel-fade@8.6.0(embla-carousel@8.6.0):
    dependencies:
      embla-carousel: 8.6.0

  embla-carousel@8.6.0: {}

  entities@6.0.0: {}

  esbuild@0.25.3:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.25.3
      '@esbuild/android-arm': 0.25.3
      '@esbuild/android-arm64': 0.25.3
      '@esbuild/android-x64': 0.25.3
      '@esbuild/darwin-arm64': 0.25.3
      '@esbuild/darwin-x64': 0.25.3
      '@esbuild/freebsd-arm64': 0.25.3
      '@esbuild/freebsd-x64': 0.25.3
      '@esbuild/linux-arm': 0.25.3
      '@esbuild/linux-arm64': 0.25.3
      '@esbuild/linux-ia32': 0.25.3
      '@esbuild/linux-loong64': 0.25.3
      '@esbuild/linux-mips64el': 0.25.3
      '@esbuild/linux-ppc64': 0.25.3
      '@esbuild/linux-riscv64': 0.25.3
      '@esbuild/linux-s390x': 0.25.3
      '@esbuild/linux-x64': 0.25.3
      '@esbuild/netbsd-arm64': 0.25.3
      '@esbuild/netbsd-x64': 0.25.3
      '@esbuild/openbsd-arm64': 0.25.3
      '@esbuild/openbsd-x64': 0.25.3
      '@esbuild/sunos-x64': 0.25.3
      '@esbuild/win32-arm64': 0.25.3
      '@esbuild/win32-ia32': 0.25.3
      '@esbuild/win32-x64': 0.25.3

  escalade@3.2.0: {}

  escape-string-regexp@5.0.0: {}

  estree-util-is-identifier-name@3.0.0: {}

  extend@3.0.2: {}

  fault@1.0.4:
    dependencies:
      format: 0.2.2

  fdir@6.4.4(picomatch@4.0.2):
    optionalDependencies:
      picomatch: 4.0.2

  format@0.2.2: {}

  fsevents@2.3.3:
    optional: true

  gensync@1.0.0-beta.2: {}

  globals@11.12.0: {}

  hast-util-from-dom@5.0.1:
    dependencies:
      '@types/hast': 3.0.4
      hastscript: 9.0.1
      web-namespaces: 2.0.1

  hast-util-from-html-isomorphic@2.0.0:
    dependencies:
      '@types/hast': 3.0.4
      hast-util-from-dom: 5.0.1
      hast-util-from-html: 2.0.3
      unist-util-remove-position: 5.0.0

  hast-util-from-html@2.0.3:
    dependencies:
      '@types/hast': 3.0.4
      devlop: 1.1.0
      hast-util-from-parse5: 8.0.3
      parse5: 7.3.0
      vfile: 6.0.3
      vfile-message: 4.0.2

  hast-util-from-parse5@8.0.3:
    dependencies:
      '@types/hast': 3.0.4
      '@types/unist': 3.0.3
      devlop: 1.1.0
      hastscript: 9.0.1
      property-information: 7.0.0
      vfile: 6.0.3
      vfile-location: 5.0.3
      web-namespaces: 2.0.1

  hast-util-is-element@3.0.0:
    dependencies:
      '@types/hast': 3.0.4

  hast-util-parse-selector@2.2.5: {}

  hast-util-parse-selector@4.0.0:
    dependencies:
      '@types/hast': 3.0.4

  hast-util-raw@9.1.0:
    dependencies:
      '@types/hast': 3.0.4
      '@types/unist': 3.0.3
      '@ungap/structured-clone': 1.3.0
      hast-util-from-parse5: 8.0.3
      hast-util-to-parse5: 8.0.0
      html-void-elements: 3.0.0
      mdast-util-to-hast: 13.2.0
      parse5: 7.3.0
      unist-util-position: 5.0.0
      unist-util-visit: 5.0.0
      vfile: 6.0.3
      web-namespaces: 2.0.1
      zwitch: 2.0.4

  hast-util-sanitize@5.0.2:
    dependencies:
      '@types/hast': 3.0.4
      '@ungap/structured-clone': 1.3.0
      unist-util-position: 5.0.0

  hast-util-to-html@9.0.5:
    dependencies:
      '@types/hast': 3.0.4
      '@types/unist': 3.0.3
      ccount: 2.0.1
      comma-separated-tokens: 2.0.3
      hast-util-whitespace: 3.0.0
      html-void-elements: 3.0.0
      mdast-util-to-hast: 13.2.0
      property-information: 7.0.0
      space-separated-tokens: 2.0.2
      stringify-entities: 4.0.4
      zwitch: 2.0.4

  hast-util-to-jsx-runtime@2.3.6:
    dependencies:
      '@types/estree': 1.0.7
      '@types/hast': 3.0.4
      '@types/unist': 3.0.3
      comma-separated-tokens: 2.0.3
      devlop: 1.1.0
      estree-util-is-identifier-name: 3.0.0
      hast-util-whitespace: 3.0.0
      mdast-util-mdx-expression: 2.0.1
      mdast-util-mdx-jsx: 3.2.0
      mdast-util-mdxjs-esm: 2.0.1
      property-information: 7.0.0
      space-separated-tokens: 2.0.2
      style-to-js: 1.1.16
      unist-util-position: 5.0.0
      vfile-message: 4.0.2
    transitivePeerDependencies:
      - supports-color

  hast-util-to-parse5@8.0.0:
    dependencies:
      '@types/hast': 3.0.4
      comma-separated-tokens: 2.0.3
      devlop: 1.1.0
      property-information: 6.5.0
      space-separated-tokens: 2.0.2
      web-namespaces: 2.0.1
      zwitch: 2.0.4

  hast-util-to-text@4.0.2:
    dependencies:
      '@types/hast': 3.0.4
      '@types/unist': 3.0.3
      hast-util-is-element: 3.0.0
      unist-util-find-after: 5.0.0

  hast-util-whitespace@3.0.0:
    dependencies:
      '@types/hast': 3.0.4

  hastscript@6.0.0:
    dependencies:
      '@types/hast': 2.3.10
      comma-separated-tokens: 1.0.8
      hast-util-parse-selector: 2.2.5
      property-information: 5.6.0
      space-separated-tokens: 1.1.5

  hastscript@9.0.1:
    dependencies:
      '@types/hast': 3.0.4
      comma-separated-tokens: 2.0.3
      hast-util-parse-selector: 4.0.0
      property-information: 7.0.0
      space-separated-tokens: 2.0.2

  highlight.js@10.7.3: {}

  highlightjs-vue@1.0.0: {}

  html-url-attributes@3.0.1: {}

  html-void-elements@3.0.0: {}

  inline-style-parser@0.2.4: {}

  is-alphabetical@1.0.4: {}

  is-alphabetical@2.0.1: {}

  is-alphanumerical@1.0.4:
    dependencies:
      is-alphabetical: 1.0.4
      is-decimal: 1.0.4

  is-alphanumerical@2.0.1:
    dependencies:
      is-alphabetical: 2.0.1
      is-decimal: 2.0.1

  is-decimal@1.0.4: {}

  is-decimal@2.0.1: {}

  is-hexadecimal@1.0.4: {}

  is-hexadecimal@2.0.1: {}

  is-plain-obj@4.1.0: {}

  isomorphic.js@0.2.5: {}

  js-tokens@4.0.0: {}

  jsesc@3.1.0: {}

  json5@2.2.3: {}

  katex@0.16.22:
    dependencies:
      commander: 8.3.0

  keyborg@2.6.0: {}

  lexical@0.12.6: {}

  lib0@0.2.105:
    dependencies:
      isomorphic.js: 0.2.5

  longest-streak@3.1.0: {}

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  lowlight@1.20.0:
    dependencies:
      fault: 1.0.4
      highlight.js: 10.7.3

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  markdown-table@3.0.4: {}

  mdast-util-find-and-replace@3.0.2:
    dependencies:
      '@types/mdast': 4.0.4
      escape-string-regexp: 5.0.0
      unist-util-is: 6.0.0
      unist-util-visit-parents: 6.0.1

  mdast-util-from-markdown@2.0.2:
    dependencies:
      '@types/mdast': 4.0.4
      '@types/unist': 3.0.3
      decode-named-character-reference: 1.1.0
      devlop: 1.1.0
      mdast-util-to-string: 4.0.0
      micromark: 4.0.2
      micromark-util-decode-numeric-character-reference: 2.0.2
      micromark-util-decode-string: 2.0.1
      micromark-util-normalize-identifier: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
      unist-util-stringify-position: 4.0.0
    transitivePeerDependencies:
      - supports-color

  mdast-util-gfm-autolink-literal@2.0.1:
    dependencies:
      '@types/mdast': 4.0.4
      ccount: 2.0.1
      devlop: 1.1.0
      mdast-util-find-and-replace: 3.0.2
      micromark-util-character: 2.1.1

  mdast-util-gfm-footnote@2.1.0:
    dependencies:
      '@types/mdast': 4.0.4
      devlop: 1.1.0
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
      micromark-util-normalize-identifier: 2.0.1
    transitivePeerDependencies:
      - supports-color

  mdast-util-gfm-strikethrough@2.0.0:
    dependencies:
      '@types/mdast': 4.0.4
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-gfm-table@2.0.0:
    dependencies:
      '@types/mdast': 4.0.4
      devlop: 1.1.0
      markdown-table: 3.0.4
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-gfm-task-list-item@2.0.0:
    dependencies:
      '@types/mdast': 4.0.4
      devlop: 1.1.0
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-gfm@3.1.0:
    dependencies:
      mdast-util-from-markdown: 2.0.2
      mdast-util-gfm-autolink-literal: 2.0.1
      mdast-util-gfm-footnote: 2.1.0
      mdast-util-gfm-strikethrough: 2.0.0
      mdast-util-gfm-table: 2.0.0
      mdast-util-gfm-task-list-item: 2.0.0
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-math@3.0.0:
    dependencies:
      '@types/hast': 3.0.4
      '@types/mdast': 4.0.4
      devlop: 1.1.0
      longest-streak: 3.1.0
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
      unist-util-remove-position: 5.0.0
    transitivePeerDependencies:
      - supports-color

  mdast-util-mdx-expression@2.0.1:
    dependencies:
      '@types/estree-jsx': 1.0.5
      '@types/hast': 3.0.4
      '@types/mdast': 4.0.4
      devlop: 1.1.0
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-mdx-jsx@3.2.0:
    dependencies:
      '@types/estree-jsx': 1.0.5
      '@types/hast': 3.0.4
      '@types/mdast': 4.0.4
      '@types/unist': 3.0.3
      ccount: 2.0.1
      devlop: 1.1.0
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
      parse-entities: 4.0.2
      stringify-entities: 4.0.4
      unist-util-stringify-position: 4.0.0
      vfile-message: 4.0.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-mdxjs-esm@2.0.1:
    dependencies:
      '@types/estree-jsx': 1.0.5
      '@types/hast': 3.0.4
      '@types/mdast': 4.0.4
      devlop: 1.1.0
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-newline-to-break@2.0.0:
    dependencies:
      '@types/mdast': 4.0.4
      mdast-util-find-and-replace: 3.0.2

  mdast-util-phrasing@4.1.0:
    dependencies:
      '@types/mdast': 4.0.4
      unist-util-is: 6.0.0

  mdast-util-to-hast@13.2.0:
    dependencies:
      '@types/hast': 3.0.4
      '@types/mdast': 4.0.4
      '@ungap/structured-clone': 1.3.0
      devlop: 1.1.0
      micromark-util-sanitize-uri: 2.0.1
      trim-lines: 3.0.1
      unist-util-position: 5.0.0
      unist-util-visit: 5.0.0
      vfile: 6.0.3

  mdast-util-to-markdown@2.1.2:
    dependencies:
      '@types/mdast': 4.0.4
      '@types/unist': 3.0.3
      longest-streak: 3.1.0
      mdast-util-phrasing: 4.1.0
      mdast-util-to-string: 4.0.0
      micromark-util-classify-character: 2.0.1
      micromark-util-decode-string: 2.0.1
      unist-util-visit: 5.0.0
      zwitch: 2.0.4

  mdast-util-to-string@4.0.0:
    dependencies:
      '@types/mdast': 4.0.4

  micromark-core-commonmark@2.0.3:
    dependencies:
      decode-named-character-reference: 1.1.0
      devlop: 1.1.0
      micromark-factory-destination: 2.0.1
      micromark-factory-label: 2.0.1
      micromark-factory-space: 2.0.1
      micromark-factory-title: 2.0.1
      micromark-factory-whitespace: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-chunked: 2.0.1
      micromark-util-classify-character: 2.0.1
      micromark-util-html-tag-name: 2.0.1
      micromark-util-normalize-identifier: 2.0.1
      micromark-util-resolve-all: 2.0.1
      micromark-util-subtokenize: 2.1.0
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-gfm-autolink-literal@2.1.0:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-sanitize-uri: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-gfm-footnote@2.1.0:
    dependencies:
      devlop: 1.1.0
      micromark-core-commonmark: 2.0.3
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-normalize-identifier: 2.0.1
      micromark-util-sanitize-uri: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-gfm-strikethrough@2.1.0:
    dependencies:
      devlop: 1.1.0
      micromark-util-chunked: 2.0.1
      micromark-util-classify-character: 2.0.1
      micromark-util-resolve-all: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-gfm-table@2.1.1:
    dependencies:
      devlop: 1.1.0
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-gfm-tagfilter@2.0.0:
    dependencies:
      micromark-util-types: 2.0.2

  micromark-extension-gfm-task-list-item@2.1.0:
    dependencies:
      devlop: 1.1.0
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-gfm@3.0.0:
    dependencies:
      micromark-extension-gfm-autolink-literal: 2.1.0
      micromark-extension-gfm-footnote: 2.1.0
      micromark-extension-gfm-strikethrough: 2.1.0
      micromark-extension-gfm-table: 2.1.1
      micromark-extension-gfm-tagfilter: 2.0.0
      micromark-extension-gfm-task-list-item: 2.1.0
      micromark-util-combine-extensions: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-math@3.1.0:
    dependencies:
      '@types/katex': 0.16.7
      devlop: 1.1.0
      katex: 0.16.22
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-factory-destination@2.0.1:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-factory-label@2.0.1:
    dependencies:
      devlop: 1.1.0
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-factory-space@2.0.1:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-types: 2.0.2

  micromark-factory-title@2.0.1:
    dependencies:
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-factory-whitespace@2.0.1:
    dependencies:
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-util-character@2.1.1:
    dependencies:
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-util-chunked@2.0.1:
    dependencies:
      micromark-util-symbol: 2.0.1

  micromark-util-classify-character@2.0.1:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-util-combine-extensions@2.0.1:
    dependencies:
      micromark-util-chunked: 2.0.1
      micromark-util-types: 2.0.2

  micromark-util-decode-numeric-character-reference@2.0.2:
    dependencies:
      micromark-util-symbol: 2.0.1

  micromark-util-decode-string@2.0.1:
    dependencies:
      decode-named-character-reference: 1.1.0
      micromark-util-character: 2.1.1
      micromark-util-decode-numeric-character-reference: 2.0.2
      micromark-util-symbol: 2.0.1

  micromark-util-encode@2.0.1: {}

  micromark-util-html-tag-name@2.0.1: {}

  micromark-util-normalize-identifier@2.0.1:
    dependencies:
      micromark-util-symbol: 2.0.1

  micromark-util-resolve-all@2.0.1:
    dependencies:
      micromark-util-types: 2.0.2

  micromark-util-sanitize-uri@2.0.1:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-encode: 2.0.1
      micromark-util-symbol: 2.0.1

  micromark-util-subtokenize@2.1.0:
    dependencies:
      devlop: 1.1.0
      micromark-util-chunked: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-util-symbol@2.0.1: {}

  micromark-util-types@2.0.2: {}

  micromark@4.0.2:
    dependencies:
      '@types/debug': 4.1.12
      debug: 4.4.0
      decode-named-character-reference: 1.1.0
      devlop: 1.1.0
      micromark-core-commonmark: 2.0.3
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-chunked: 2.0.1
      micromark-util-combine-extensions: 2.0.1
      micromark-util-decode-numeric-character-reference: 2.0.2
      micromark-util-encode: 2.0.1
      micromark-util-normalize-identifier: 2.0.1
      micromark-util-resolve-all: 2.0.1
      micromark-util-sanitize-uri: 2.0.1
      micromark-util-subtokenize: 2.1.0
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
    transitivePeerDependencies:
      - supports-color

  ms@2.1.3: {}

  nanoid@3.3.11: {}

  node-releases@2.0.19: {}

  object-assign@4.1.1: {}

  parse-entities@2.0.0:
    dependencies:
      character-entities: 1.2.4
      character-entities-legacy: 1.1.4
      character-reference-invalid: 1.1.4
      is-alphanumerical: 1.0.4
      is-decimal: 1.0.4
      is-hexadecimal: 1.0.4

  parse-entities@4.0.2:
    dependencies:
      '@types/unist': 2.0.11
      character-entities-legacy: 3.0.0
      character-reference-invalid: 2.0.1
      decode-named-character-reference: 1.1.0
      is-alphanumerical: 2.0.1
      is-decimal: 2.0.1
      is-hexadecimal: 2.0.1

  parse5@7.3.0:
    dependencies:
      entities: 6.0.0

  picocolors@1.1.1: {}

  picomatch@4.0.2: {}

  postcss@8.5.3:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  prismjs@1.30.0: {}

  prop-types@15.8.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  property-information@5.6.0:
    dependencies:
      xtend: 4.0.2

  property-information@6.5.0: {}

  property-information@7.0.0: {}

  react-dom@19.1.0(react@19.1.0):
    dependencies:
      react: 19.1.0
      scheduler: 0.26.0

  react-error-boundary@3.1.4(react@19.1.0):
    dependencies:
      '@babel/runtime': 7.27.1
      react: 19.1.0

  react-is@16.13.1: {}

  react-is@17.0.2: {}

  react-markdown@10.1.0(@types/react@18.3.20)(react@19.1.0):
    dependencies:
      '@types/hast': 3.0.4
      '@types/mdast': 4.0.4
      '@types/react': 18.3.20
      devlop: 1.1.0
      hast-util-to-jsx-runtime: 2.3.6
      html-url-attributes: 3.0.1
      mdast-util-to-hast: 13.2.0
      react: 19.1.0
      remark-parse: 11.0.0
      remark-rehype: 11.1.2
      unified: 11.0.5
      unist-util-visit: 5.0.0
      vfile: 6.0.3
    transitivePeerDependencies:
      - supports-color

  react-refresh@0.17.0: {}

  react-syntax-highlighter@15.6.1(react@19.1.0):
    dependencies:
      '@babel/runtime': 7.27.1
      highlight.js: 10.7.3
      highlightjs-vue: 1.0.0
      lowlight: 1.20.0
      prismjs: 1.30.0
      react: 19.1.0
      refractor: 3.6.0

  react-transition-group@4.4.5(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    dependencies:
      '@babel/runtime': 7.27.1
      dom-helpers: 5.2.1
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  react@19.1.0: {}

  refractor@3.6.0:
    dependencies:
      hastscript: 6.0.0
      parse-entities: 2.0.0
      prismjs: 1.30.0

  rehype-katex@7.0.1:
    dependencies:
      '@types/hast': 3.0.4
      '@types/katex': 0.16.7
      hast-util-from-html-isomorphic: 2.0.0
      hast-util-to-text: 4.0.2
      katex: 0.16.22
      unist-util-visit-parents: 6.0.1
      vfile: 6.0.3

  rehype-raw@7.0.0:
    dependencies:
      '@types/hast': 3.0.4
      hast-util-raw: 9.1.0
      vfile: 6.0.3

  rehype-sanitize@6.0.0:
    dependencies:
      '@types/hast': 3.0.4
      hast-util-sanitize: 5.0.2

  rehype-stringify@10.0.1:
    dependencies:
      '@types/hast': 3.0.4
      hast-util-to-html: 9.0.5
      unified: 11.0.5

  remark-breaks@4.0.0:
    dependencies:
      '@types/mdast': 4.0.4
      mdast-util-newline-to-break: 2.0.0
      unified: 11.0.5

  remark-gfm@4.0.1:
    dependencies:
      '@types/mdast': 4.0.4
      mdast-util-gfm: 3.1.0
      micromark-extension-gfm: 3.0.0
      remark-parse: 11.0.0
      remark-stringify: 11.0.0
      unified: 11.0.5
    transitivePeerDependencies:
      - supports-color

  remark-math@6.0.0:
    dependencies:
      '@types/mdast': 4.0.4
      mdast-util-math: 3.0.0
      micromark-extension-math: 3.1.0
      unified: 11.0.5
    transitivePeerDependencies:
      - supports-color

  remark-parse@11.0.0:
    dependencies:
      '@types/mdast': 4.0.4
      mdast-util-from-markdown: 2.0.2
      micromark-util-types: 2.0.2
      unified: 11.0.5
    transitivePeerDependencies:
      - supports-color

  remark-rehype@11.1.2:
    dependencies:
      '@types/hast': 3.0.4
      '@types/mdast': 4.0.4
      mdast-util-to-hast: 13.2.0
      unified: 11.0.5
      vfile: 6.0.3

  remark-stringify@11.0.0:
    dependencies:
      '@types/mdast': 4.0.4
      mdast-util-to-markdown: 2.1.2
      unified: 11.0.5

  remark-supersub@1.0.0:
    dependencies:
      unist-util-visit: 4.1.2

  rollup@4.40.0:
    dependencies:
      '@types/estree': 1.0.7
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': 4.40.0
      '@rollup/rollup-android-arm64': 4.40.0
      '@rollup/rollup-darwin-arm64': 4.40.0
      '@rollup/rollup-darwin-x64': 4.40.0
      '@rollup/rollup-freebsd-arm64': 4.40.0
      '@rollup/rollup-freebsd-x64': 4.40.0
      '@rollup/rollup-linux-arm-gnueabihf': 4.40.0
      '@rollup/rollup-linux-arm-musleabihf': 4.40.0
      '@rollup/rollup-linux-arm64-gnu': 4.40.0
      '@rollup/rollup-linux-arm64-musl': 4.40.0
      '@rollup/rollup-linux-loongarch64-gnu': 4.40.0
      '@rollup/rollup-linux-powerpc64le-gnu': 4.40.0
      '@rollup/rollup-linux-riscv64-gnu': 4.40.0
      '@rollup/rollup-linux-riscv64-musl': 4.40.0
      '@rollup/rollup-linux-s390x-gnu': 4.40.0
      '@rollup/rollup-linux-x64-gnu': 4.40.0
      '@rollup/rollup-linux-x64-musl': 4.40.0
      '@rollup/rollup-win32-arm64-msvc': 4.40.0
      '@rollup/rollup-win32-ia32-msvc': 4.40.0
      '@rollup/rollup-win32-x64-msvc': 4.40.0
      fsevents: 2.3.3

  rtl-css-js@1.16.1:
    dependencies:
      '@babel/runtime': 7.27.1

  scheduler@0.26.0: {}

  semver@6.3.1: {}

  source-map-js@1.2.1: {}

  space-separated-tokens@1.1.5: {}

  space-separated-tokens@2.0.2: {}

  stringify-entities@4.0.4:
    dependencies:
      character-entities-html4: 2.1.0
      character-entities-legacy: 3.0.0

  style-to-js@1.1.16:
    dependencies:
      style-to-object: 1.0.8

  style-to-object@1.0.8:
    dependencies:
      inline-style-parser: 0.2.4

  stylis@4.3.6: {}

  tabster@8.5.4:
    dependencies:
      keyborg: 2.6.0
      tslib: 2.8.1

  tinyglobby@0.2.13:
    dependencies:
      fdir: 6.4.4(picomatch@4.0.2)
      picomatch: 4.0.2

  toggle-selection@1.0.6: {}

  trim-lines@3.0.1: {}

  trough@2.2.0: {}

  tslib@2.8.1: {}

  typescript@5.8.3: {}

  undici-types@6.21.0: {}

  unified@11.0.5:
    dependencies:
      '@types/unist': 3.0.3
      bail: 2.0.2
      devlop: 1.1.0
      extend: 3.0.2
      is-plain-obj: 4.1.0
      trough: 2.2.0
      vfile: 6.0.3

  unist-util-find-after@5.0.0:
    dependencies:
      '@types/unist': 3.0.3
      unist-util-is: 6.0.0

  unist-util-is@5.2.1:
    dependencies:
      '@types/unist': 2.0.11

  unist-util-is@6.0.0:
    dependencies:
      '@types/unist': 3.0.3

  unist-util-position@5.0.0:
    dependencies:
      '@types/unist': 3.0.3

  unist-util-remove-position@5.0.0:
    dependencies:
      '@types/unist': 3.0.3
      unist-util-visit: 5.0.0

  unist-util-stringify-position@4.0.0:
    dependencies:
      '@types/unist': 3.0.3

  unist-util-visit-parents@5.1.3:
    dependencies:
      '@types/unist': 2.0.11
      unist-util-is: 5.2.1

  unist-util-visit-parents@6.0.1:
    dependencies:
      '@types/unist': 3.0.3
      unist-util-is: 6.0.0

  unist-util-visit@4.1.2:
    dependencies:
      '@types/unist': 2.0.11
      unist-util-is: 5.2.1
      unist-util-visit-parents: 5.1.3

  unist-util-visit@5.0.0:
    dependencies:
      '@types/unist': 3.0.3
      unist-util-is: 6.0.0
      unist-util-visit-parents: 6.0.1

  update-browserslist-db@1.1.3(browserslist@4.24.4):
    dependencies:
      browserslist: 4.24.4
      escalade: 3.2.0
      picocolors: 1.1.1

  use-disposable@1.0.4(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    dependencies:
      '@types/react': 18.3.20
      '@types/react-dom': 18.3.5(@types/react@18.3.20)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  use-sync-external-store@1.5.0(react@19.1.0):
    dependencies:
      react: 19.1.0

  vfile-location@5.0.3:
    dependencies:
      '@types/unist': 3.0.3
      vfile: 6.0.3

  vfile-message@4.0.2:
    dependencies:
      '@types/unist': 3.0.3
      unist-util-stringify-position: 4.0.0

  vfile@6.0.3:
    dependencies:
      '@types/unist': 3.0.3
      vfile-message: 4.0.2

  vite@6.3.4(@types/node@22.14.1):
    dependencies:
      esbuild: 0.25.3
      fdir: 6.4.4(picomatch@4.0.2)
      picomatch: 4.0.2
      postcss: 8.5.3
      rollup: 4.40.0
      tinyglobby: 0.2.13
    optionalDependencies:
      '@types/node': 22.14.1
      fsevents: 2.3.3

  web-namespaces@2.0.1: {}

  xtend@4.0.2: {}

  yallist@3.1.1: {}

  yjs@13.6.26:
    dependencies:
      lib0: 0.2.105

  zwitch@2.0.4: {}
