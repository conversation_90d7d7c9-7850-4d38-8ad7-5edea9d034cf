#!/usr/bin/env python3
"""
Test script to verify Azure AI Agent connection
"""
import os
from dotenv import load_dotenv
from azure.ai.projects import AIProjectClient
from azure.identity import DefaultAzureCredential
from azure.ai.agents.models import ListSortOrder

# Load environment variables
load_dotenv('src/.env')

def test_agent_connection():
    try:
        # Initialize the project client
        project = AIProjectClient(
            credential=DefaultAzureCredential(),
            endpoint=os.getenv("AZURE_EXISTING_AIPROJECT_ENDPOINT")
        )
        
        # Get the agent
        agent_id = os.getenv("AZURE_EXISTING_AGENT_ID")
        agent = project.agents.get_agent(agent_id)
        print(f"✅ Successfully connected to agent: {agent.name}")
        print(f"   Agent ID: {agent.id}")
        print(f"   Model: {agent.model}")
        
        # Test creating a thread and sending a message
        thread = project.agents.threads.create()
        print(f"✅ Created thread, ID: {thread.id}")

        message = project.agents.messages.create(
            thread_id=thread.id,
            role="user",
            content="Hello! Can you confirm you're working?"
        )

        run = project.agents.runs.create_and_process(
            thread_id=thread.id,
            agent_id=agent.id
        )

        if run.status == "failed":
            print(f"❌ Run failed: {run.last_error}")
            return False
        else:
            messages = project.agents.messages.list(thread_id=thread.id, order=ListSortOrder.ASCENDING)
            
            print("✅ Conversation:")
            for message in messages:
                if message.text_messages:
                    print(f"   {message.role}: {message.text_messages[-1].text.value}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error connecting to agent: {str(e)}")
        return False

if __name__ == "__main__":
    print("Testing Azure AI Agent connection...")
    success = test_agent_connection()
    if success:
        print("\n🎉 Agent connection test successful! You can now run the application.")
    else:
        print("\n💥 Agent connection test failed. Please check your configuration.")
